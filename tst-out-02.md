oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.core.test.ts --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.core.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.core.test.ts (370 MB heap size)
  AtomicCircularBuffer - Core Functionality
    Constructor and Initialization
      ✓ should create buffer with valid max size (4 ms)
      ✓ should initialize with empty state (2 ms)
      ✓ should handle zero max size (2 ms)
      ✓ should handle large max size (2 ms)
    Basic Add Operations
      ✓ should add single item correctly (6 ms)
      ✓ should add multiple items correctly (2 ms)
      ✓ should handle duplicate keys by updating values (2 ms)
      ✓ should handle empty string keys (2 ms)
      ✓ should handle special character keys (2 ms)
    Basic Remove Operations
      ✓ should remove existing item correctly (3 ms)
      ✓ should return false for non-existent key (3 ms)
      ✓ should handle removing all items (2 ms)
    Basic Get Operations
      ✓ should retrieve existing items correctly (2 ms)
      ✓ should return undefined for non-existent keys (7 ms)
      ✓ should handle getAllItems correctly (2 ms)
      ✓ should return defensive copy of all items (2 ms)
    Size Management
      ✓ should track size accurately (2 ms)
      ✓ should enforce maximum size limits (2 ms)
      ✓ should handle size correctly with duplicate keys (2 ms)
    Circular Buffer Behavior
      ✓ should implement circular behavior when full (2 ms)
      ✓ should maintain insertion order in circular behavior (2 ms)
    Resource Management Integration
      ✓ should integrate with MemorySafeResourceManager (2 ms)
      ✓ should handle initialization and shutdown properly (2 ms)
    Basic Metrics
      ✓ should initialize metrics with zero values (6 ms)
      ✓ should track basic operations in metrics (2 ms)

Test Suites: 1 passed, 1 total
Tests:       25 passed, 25 total
Snapshots:   0 total
Time:        3.362 s, estimated 4 s
Ran all test suites matching /AtomicCircularBuffer.core.test.ts/i.
dv@lnv:~/dev/web-dev/oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.memory.test.ts --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.memory.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.memory.test.ts (376 MB heap size)
  AtomicCircularBuffer - Memory Safety
    Memory Leak Detection
      ✓ should not leak memory during normal operations (7 ms)
      ✓ should properly clean up large datasets (77 ms)
      ✓ should handle memory pressure gracefully (3 ms)
    Resource Cleanup Verification
      ✓ should properly clean up intervals and timeouts (2 ms)
      ✓ should handle shutdown during active operations (19 ms)
    Memory Boundary Enforcement
      ✓ should enforce maximum size limits (2 ms)
      ✓ should handle zero-size buffer gracefully (2 ms)
    Memory Monitoring Integration
      ✓ should provide accurate resource metrics (1 ms)
      ✓ should track memory usage patterns (2 ms)

Test Suites: 1 passed, 1 total
Tests:       9 passed, 9 total
Snapshots:   0 total
Time:        3.492 s, estimated 4 s
Ran all test suites matching /AtomicCircularBuffer.memory.test.ts/i.
dv@lnv:~/dev/web-dev/oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.performance.test.ts --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.performance.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.performance.test.ts (375 MB heap size)
  AtomicCircularBuffer - Performance
    Performance Benchmarks
      ✓ should maintain fast add operations under load (6 ms)
      ✓ should maintain fast get operations under load (51 ms)
      ✓ should maintain fast remove operations under load (7 ms)
      ✓ should handle mixed operations efficiently (4 ms)
    Concurrent Access Performance
      ✓ should handle basic concurrent operations (18 ms)
      ✓ should handle concurrent additions without corruption (8 ms)
      ✓ should handle rapid key updates without data loss (13 ms)
    Stress Testing
      ✓ should handle high-concurrency stress test (5 ms)
      ✓ should maintain performance with large datasets (55 ms)
    Performance Metrics
      ✓ should track operation metrics accurately (8 ms)
      ✓ should provide performance insights through metrics (2 ms)

Test Suites: 1 passed, 1 total
Tests:       11 passed, 11 total
Snapshots:   0 total
Time:        3.46 s, estimated 4 s
Ran all test suites matching /AtomicCircularBuffer.performance.test.ts/i.
dv@lnv:~/dev/web-dev/oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.integration.test.ts --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.integration.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.integration.test.ts (374 MB heap size)
  AtomicCircularBuffer - Integration
    MemorySafeResourceManager Integration
      ✓ should integrate properly with MemorySafeResourceManager (5 ms)
      ✓ should handle resource lifecycle properly (8 ms)
      ✓ should handle shared resource scenarios (2 ms)
    Logging Integration
      ✓ should implement ILoggingService interface correctly (2 ms)
      ✓ should integrate logging with buffer operations (2 ms)
    Error Handling Integration
      ✓ should handle operations on uninitialized buffer (2 ms)
      ✓ should handle invalid input gracefully (3 ms)
      ✓ should recover from temporary errors (2 ms)
    End-to-End Workflow Scenarios
      ✓ should handle complete lifecycle workflow (3 ms)
      ✓ should handle concurrent workflow scenarios (22 ms)
      ✓ should handle stress workflow with recovery (7 ms)
    Cross-Component Integration
      ✓ should work with multiple buffer instances (2 ms)

Test Suites: 1 passed, 1 total
Tests:       12 passed, 12 total
Snapshots:   0 total
Time:        3.369 s, estimated 4 s
Ran all test suites matching /AtomicCircularBuffer.integration.test.ts/i.
