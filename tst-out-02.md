
# Test Output: AtomicCircularBuffer Core Test Suite

oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.core.test.ts --verbose --no-coverage --forceExit

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.core.test.ts --verbose --no-coverage --forceExit

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/AtomicCircularBuffer.core.test.ts (373 MB heap size)
  AtomicCircularBuffer - Core Functionality
    Constructor and Initialization
      ✓ should create buffer with valid max size (6 ms)
      ✓ should initialize with empty state (2 ms)
      ✓ should handle zero max size (2 ms)
      ✓ should handle large max size (2 ms)
    Basic Add Operations
      ✓ should add single item correctly (3 ms)
      ✓ should add multiple items correctly (2 ms)
      ✓ should handle duplicate keys by updating values (3 ms)
      ✓ should handle empty string keys (8 ms)
      ✓ should handle special character keys (2 ms)
    Basic Remove Operations
      ✕ should remove existing item correctly (5 ms)
      ✕ should return undefined for non-existent key (3 ms)
      ✓ should handle removing all items (2 ms)
    Basic Get Operations
      ✓ should retrieve existing items correctly (2 ms)
      ✓ should return undefined for non-existent keys (3 ms)
      ✓ should handle getAllItems correctly (2 ms)
      ✓ should return defensive copy of all items (8 ms)
    Size Management
      ✓ should track size accurately (2 ms)
      ✓ should enforce maximum size limits (2 ms)
      ✓ should handle size correctly with duplicate keys (2 ms)
    Circular Buffer Behavior
      ✓ should implement circular behavior when full (1 ms)
      ✓ should maintain insertion order in circular behavior (2 ms)
    Resource Management Integration
      ✓ should integrate with MemorySafeResourceManager (2 ms)
      ✓ should handle initialization and shutdown properly (2 ms)
    Basic Metrics
      ✓ should initialize metrics with zero values (2 ms)
      ✓ should track basic operations in metrics (2 ms)

  ● AtomicCircularBuffer - Core Functionality › Basic Remove Operations › should remove existing item correctly

    expect(received).toBe(expected) // Object.is equality

    Expected: "value2"
    Received: true

      114 |     it('should remove existing item correctly', async () => {
      115 |       const removed = await buffer.removeItem('key2');
    > 116 |       expect(removed).toBe('value2');
          |                       ^
      117 |       expect(buffer.getSize()).toBe(2);
      118 |       expect(buffer.getItem('key2')).toBeUndefined();
      119 |     });

      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.core.test.ts:116:23)

  ● AtomicCircularBuffer - Core Functionality › Basic Remove Operations › should return undefined for non-existent key

    expect(received).toBeUndefined()

    Received: false

      121 |     it('should return undefined for non-existent key', async () => {
      122 |       const removed = await buffer.removeItem('nonexistent');
    > 123 |       expect(removed).toBeUndefined();
          |                       ^
      124 |       expect(buffer.getSize()).toBe(3); // Size should remain unchanged
      125 |     });
      126 |

      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.core.test.ts:123:23)

Test Suites: 1 failed, 1 total
Tests:       2 failed, 23 passed, 25 total
Snapshots:   0 total
Time:        3.446 s
Ran all test suites matching /AtomicCircularBuffer.core.test.ts/i.

---

# Test Output: AtomicCircularBuffer Memory Safety Test Suite

oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.memory.test.ts --verbose --no-coverage --forceExit

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.memory.test.ts --verbose --no-coverage --forceExit

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/AtomicCircularBuffer.memory.test.ts (371 MB heap size)
  AtomicCircularBuffer - Memory Safety
    Memory Leak Detection
      ✓ should not leak memory during normal operations (6 ms)
      ✕ should properly clean up large datasets (72 ms)
      ✓ should handle memory pressure gracefully (3 ms)
    Resource Cleanup Verification
      ✓ should properly clean up intervals and timeouts (2 ms)
      ✓ should handle shutdown during active operations (25 ms)
    Memory Boundary Enforcement
      ✓ should enforce maximum size limits (2 ms)
      ✓ should handle zero-size buffer gracefully (2 ms)
    Memory Monitoring Integration
      ✓ should provide accurate resource metrics (2 ms)
      ✓ should track memory usage patterns (3 ms)

  ● AtomicCircularBuffer - Memory Safety › Memory Leak Detection › should properly clean up large datasets

    expect(received).toBeGreaterThan(expected)

    Expected: > 50
    Received:   0.4208515946985977

      194 |
      195 |       // Memory should be effectively cleaned up
    > 196 |       expect(metrics.gcEffectiveness).toBeGreaterThan(50); // At least 50% GC effectiveness
          |                                       ^
      197 |     });
      198 |
      199 |     it('should handle memory pressure gracefully', async () => {

      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.memory.test.ts:196:39)

Test Suites: 1 failed, 1 total
Tests:       1 failed, 8 passed, 9 total
Snapshots:   0 total
Time:        3.326 s
Ran all test suites matching /AtomicCircularBuffer.memory.test.ts/i.

---
# Test Output: AtomicCircularBuffer Performance Test Suite

oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.performance.test.ts --verbose --no-coverage --forceExit

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.performance.test.ts --verbose --no-coverage --forceExit

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.performance.test.ts (374 MB heap size)
  AtomicCircularBuffer - Performance
    Performance Benchmarks
      ✓ should maintain fast add operations under load (11 ms)
      ✓ should maintain fast get operations under load (49 ms)
      ✓ should maintain fast remove operations under load (5 ms)
      ✓ should handle mixed operations efficiently (5 ms)
    Concurrent Access Performance
      ✓ should handle basic concurrent operations (22 ms)
      ✓ should handle concurrent additions without corruption (13 ms)
      ✓ should handle rapid key updates without data loss (7 ms)
    Stress Testing
      ✓ should handle high-concurrency stress test (4 ms)
      ✓ should maintain performance with large datasets (59 ms)
    Performance Metrics
      ✓ should track operation metrics accurately (2 ms)
      ✓ should provide performance insights through metrics (2 ms)

Test Suites: 1 passed, 1 total
Tests:       11 passed, 11 total
Snapshots:   0 total
Time:        3.571 s
Ran all test suites matching /AtomicCircularBuffer.performance.test.ts/i.

---

oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.integration.test.ts --verbose --no-coverage --forceExit

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.integration.test.ts --verbose --no-coverage --forceExit

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.integration.test.ts (369 MB heap size)
  AtomicCircularBuffer - Integration
    MemorySafeResourceManager Integration
      ✓ should integrate properly with MemorySafeResourceManager (6 ms)
      ✓ should handle resource lifecycle properly (4 ms)
      ✓ should handle shared resource scenarios (3 ms)
    Logging Integration
      ✓ should implement ILoggingService interface correctly (3 ms)
      ✓ should integrate logging with buffer operations (6 ms)
    Error Handling Integration
      ✓ should handle operations on uninitialized buffer (2 ms)
      ✓ should handle invalid input gracefully (3 ms)
      ✓ should recover from temporary errors (2 ms)
    End-to-End Workflow Scenarios
      ✓ should handle complete lifecycle workflow (3 ms)
      ✓ should handle concurrent workflow scenarios (16 ms)
      ✓ should handle stress workflow with recovery (12 ms)
    Cross-Component Integration
      ✓ should work with multiple buffer instances (2 ms)

Test Suites: 1 passed, 1 total
Tests:       12 passed, 12 total
Snapshots:   0 total
Time:        3.317 s
Ran all test suites matching /AtomicCircularBuffer.integration.test.ts/i.