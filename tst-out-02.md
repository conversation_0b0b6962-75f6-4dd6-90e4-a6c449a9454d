oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.memory.test.ts --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.memory.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/AtomicCircularBuffer.memory.test.ts (379 MB heap size)
  AtomicCircularBuffer - Memory Safety
    Memory Leak Detection
      ✓ should not leak memory during normal operations (6 ms)
      ✓ should properly clean up large datasets (72 ms)
      ✓ should handle memory pressure gracefully (3 ms)
    Resource Cleanup Verification
      ✕ should properly clean up intervals and timeouts (4 ms)
      ✕ should handle shutdown during active operations (28 ms)
    Memory Boundary Enforcement
      ✓ should enforce maximum size limits (2 ms)
      ✓ should handle zero-size buffer gracefully (2 ms)
    Memory Monitoring Integration
      ✓ should provide accurate resource metrics (2 ms)
      ✓ should track memory usage patterns (3 ms)

  ● AtomicCircularBuffer - Memory Safety › Resource Cleanup Verification › should properly clean up intervals and timeouts

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      403 |
      404 |       // After shutdown, buffer should not be healthy
    > 405 |       expect(testBuffer.isHealthy()).toBe(false);
          |                                      ^
      406 |     });
      407 |
      408 |     it('should handle shutdown during active operations', async () => {

      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.memory.test.ts:405:38)

  ● AtomicCircularBuffer - Memory Safety › Resource Cleanup Verification › should handle shutdown during active operations

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      423 |
      424 |       // Buffer should be properly shut down
    > 425 |       expect(testBuffer.isHealthy()).toBe(false);
          |                                      ^
      426 |     });
      427 |   });
      428 |

      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.memory.test.ts:425:38)

Test Suites: 1 failed, 1 total
Tests:       2 failed, 7 passed, 9 total
Snapshots:   0 total
Time:        3.409 s, estimated 4 s
Ran all test suites matching /AtomicCircularBuffer.memory.test.ts/i.

oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.integration.test.ts --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.integration.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/AtomicCircularBuffer.integration.test.ts (372 MB heap size)
  AtomicCircularBuffer - Integration
    MemorySafeResourceManager Integration
      ✓ should integrate properly with MemorySafeResourceManager (5 ms)
      ✕ should handle resource lifecycle properly (5 ms)
      ✓ should handle shared resource scenarios (7 ms)
    Logging Integration
      ✓ should implement ILoggingService interface correctly (2 ms)
      ✓ should integrate logging with buffer operations (3 ms)
    Error Handling Integration
      ✓ should handle operations on uninitialized buffer (2 ms)
      ✓ should handle invalid input gracefully (4 ms)
      ✓ should recover from temporary errors (2 ms)
    End-to-End Workflow Scenarios
      ✓ should handle complete lifecycle workflow (2 ms)
      ✓ should handle concurrent workflow scenarios (22 ms)
      ✓ should handle stress workflow with recovery (7 ms)
    Cross-Component Integration
      ✓ should work with multiple buffer instances (2 ms)

  ● AtomicCircularBuffer - Integration › MemorySafeResourceManager Integration › should handle resource lifecycle properly

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      222 |         for (const testBuffer of buffers) {
      223 |           await testBuffer.shutdown();
    > 224 |           expect(testBuffer.isHealthy()).toBe(false);
          |                                          ^
      225 |         }
      226 |       }
      227 |     });

      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.integration.test.ts:224:42)

Test Suites: 1 failed, 1 total
Tests:       1 failed, 11 passed, 12 total
Snapshots:   0 total
Time:        3.352 s
Ran all test suites matching /AtomicCircularBuffer.integration.test.ts/i.
