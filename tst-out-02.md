oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.core.test.ts --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.core.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.core.test.ts (375 MB heap size)
  AtomicCircularBuffer - Core Functionality
    Constructor and Initialization
      ✓ should create buffer with valid max size (5 ms)
      ✓ should initialize with empty state (3 ms)
      ✓ should handle zero max size (2 ms)
      ✓ should handle large max size (3 ms)
    Basic Add Operations
      ✓ should add single item correctly (2 ms)
      ✓ should add multiple items correctly (2 ms)
      ✓ should handle duplicate keys by updating values (2 ms)
      ✓ should handle empty string keys (2 ms)
      ✓ should handle special character keys (6 ms)
    Basic Remove Operations
      ✓ should remove existing item correctly (3 ms)
      ✓ should return false for non-existent key (3 ms)
      ✓ should handle removing all items (2 ms)
    Basic Get Operations
      ✓ should retrieve existing items correctly (2 ms)
      ✓ should return undefined for non-existent keys (2 ms)
      ✓ should handle getAllItems correctly (3 ms)
      ✓ should return defensive copy of all items (2 ms)
    Size Management
      ✓ should track size accurately (2 ms)
      ✓ should enforce maximum size limits (6 ms)
      ✓ should handle size correctly with duplicate keys (2 ms)
    Circular Buffer Behavior
      ✓ should implement circular behavior when full (2 ms)
      ✓ should maintain insertion order in circular behavior (2 ms)
    Resource Management Integration
      ✓ should integrate with MemorySafeResourceManager (2 ms)
      ✓ should handle initialization and shutdown properly (2 ms)
    Basic Metrics
      ✓ should initialize metrics with zero values (2 ms)
      ✓ should track basic operations in metrics (2 ms)

Test Suites: 1 passed, 1 total
Tests:       25 passed, 25 total
Snapshots:   0 total
Time:        3.345 s, estimated 4 s
Ran all test suites matching /AtomicCircularBuffer.core.test.ts/i.

oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.integration.test.ts --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.integration.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/AtomicCircularBuffer.integration.test.ts (375 MB heap size)
  AtomicCircularBuffer - Integration
    MemorySafeResourceManager Integration
      ✓ should integrate properly with MemorySafeResourceManager (5 ms)
      ✕ should handle resource lifecycle properly (5 ms)
      ✓ should handle shared resource scenarios (3 ms)
    Logging Integration
      ✓ should implement ILoggingService interface correctly (2 ms)
      ✓ should integrate logging with buffer operations (2 ms)
    Error Handling Integration
      ✓ should handle operations on uninitialized buffer (2 ms)
      ✓ should handle invalid input gracefully (4 ms)
      ✓ should recover from temporary errors (2 ms)
    End-to-End Workflow Scenarios
      ✓ should handle complete lifecycle workflow (3 ms)
      ✓ should handle concurrent workflow scenarios (21 ms)
      ✓ should handle stress workflow with recovery (7 ms)
    Cross-Component Integration
      ✓ should work with multiple buffer instances (2 ms)

  ● AtomicCircularBuffer - Integration › MemorySafeResourceManager Integration › should handle resource lifecycle properly

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      221 |         for (const testBuffer of buffers) {
      222 |           await testBuffer.shutdown();
    > 223 |           expect(testBuffer.isHealthy()).toBe(false);
          |                                          ^
      224 |         }
      225 |       }
      226 |     });

      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.integration.test.ts:223:42)

Test Suites: 1 failed, 1 total
Tests:       1 failed, 11 passed, 12 total
Snapshots:   0 total
Time:        3.35 s
Ran all test suites matching /AtomicCircularBuffer.integration.test.ts/i.

oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.memory.test.ts --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.memory.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/AtomicCircularBuffer.memory.test.ts (378 MB heap size)
  AtomicCircularBuffer - Memory Safety
    Memory Leak Detection
      ✓ should not leak memory during normal operations (7 ms)
      ✓ should properly clean up large datasets (73 ms)
      ✓ should handle memory pressure gracefully (3 ms)
    Resource Cleanup Verification
      ✕ should properly clean up intervals and timeouts (4 ms)
      ✕ should handle shutdown during active operations (17 ms)
    Memory Boundary Enforcement
      ✓ should enforce maximum size limits (2 ms)
      ✓ should handle zero-size buffer gracefully (2 ms)
    Memory Monitoring Integration
      ✓ should provide accurate resource metrics (2 ms)
      ✓ should track memory usage patterns (2 ms)

  ● AtomicCircularBuffer - Memory Safety › Resource Cleanup Verification › should properly clean up intervals and timeouts

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      403 |
      404 |       // After shutdown, buffer should not be healthy
    > 405 |       expect(testBuffer.isHealthy()).toBe(false);
          |                                      ^
      406 |     });
      407 |
      408 |     it('should handle shutdown during active operations', async () => {

      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.memory.test.ts:405:38)

  ● AtomicCircularBuffer - Memory Safety › Resource Cleanup Verification › should handle shutdown during active operations

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      423 |
      424 |       // Buffer should be properly shut down
    > 425 |       expect(testBuffer.isHealthy()).toBe(false);
          |                                      ^
      426 |     });
      427 |   });
      428 |

      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.memory.test.ts:425:38)

Test Suites: 1 failed, 1 total
Tests:       2 failed, 7 passed, 9 total
Snapshots:   0 total
Time:        3.447 s, estimated 4 s
Ran all test suites matching /AtomicCircularBuffer.memory.test.ts/i.

oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.performance.test.ts --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.performance.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.performance.test.ts (373 MB heap size)
  AtomicCircularBuffer - Performance
    Performance Benchmarks
      ✓ should maintain fast add operations under load (7 ms)
      ✓ should maintain fast get operations under load (51 ms)
      ✓ should maintain fast remove operations under load (7 ms)
      ✓ should handle mixed operations efficiently (4 ms)
    Concurrent Access Performance
      ✓ should handle basic concurrent operations (23 ms)
      ✓ should handle concurrent additions without corruption (9 ms)
      ✓ should handle rapid key updates without data loss (12 ms)
    Stress Testing
      ✓ should handle high-concurrency stress test (5 ms)
      ✓ should maintain performance with large datasets (55 ms)
    Performance Metrics
      ✓ should track operation metrics accurately (8 ms)
      ✓ should provide performance insights through metrics (3 ms)

Test Suites: 1 passed, 1 total
Tests:       11 passed, 11 total
Snapshots:   0 total
Time:        3.448 s, estimated 4 s
Ran all test suites matching /AtomicCircularBuffer.performance.test.ts/i.
