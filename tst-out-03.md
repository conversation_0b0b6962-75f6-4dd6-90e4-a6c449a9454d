Running coverage on untested files...Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/analytics-engines/GovernanceRuleAnalyticsEngine.ts
ERROR: server/src/platform/governance/analytics-engines/GovernanceRuleAnalyticsEngine.ts:474:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

474       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/analytics-engines/GovernanceRuleReportingEngine.ts
ERROR: server/src/platform/governance/analytics-engines/GovernanceRuleReportingEngine.ts:418:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

418       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/automation-processing/GovernanceRuleEventManager.ts
ERROR: server/src/platform/governance/automation-processing/GovernanceRuleEventManager.ts:219:66 - error TS2339: Property 'getInstance' does not exist on type 'typeof EnvironmentConstantsCalculator'.

219     this._environmentCalculator = EnvironmentConstantsCalculator.getInstance();
                                                                     ~~~~~~~~~~~
server/src/platform/governance/automation-processing/GovernanceRuleEventManager.ts:241:41 - error TS2339: Property 'enforceMemoryBoundaries' does not exist on type 'EnvironmentConstantsCalculator'.

241       await this._environmentCalculator.enforceMemoryBoundaries();
                                            ~~~~~~~~~~~~~~~~~~~~~~~
server/src/platform/governance/automation-processing/GovernanceRuleEventManager.ts:757:61 - error TS2339: Property 'getSystemHealthMetrics' does not exist on type 'EnvironmentConstantsCalculator'.

757     const healthMetrics = await this._environmentCalculator.getSystemHealthMetrics();
                                                                ~~~~~~~~~~~~~~~~~~~~~~
server/src/platform/governance/automation-processing/GovernanceRuleEventManager.ts:1154:64 - error TS2339: Property 'validateMemoryConstraints' does not exist on type 'EnvironmentConstantsCalculator'.

1154     const memoryValidation = await this._environmentCalculator.validateMemoryConstraints();
                                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/automation-processing/GovernanceRuleTransformationEngine.ts
ERROR: server/src/platform/governance/automation-processing/GovernanceRuleTransformationEngine.ts:169:66 - error TS2339: Property 'getInstance' does not exist on type 'typeof EnvironmentConstantsCalculator'.

169     this._environmentCalculator = EnvironmentConstantsCalculator.getInstance();
                                                                     ~~~~~~~~~~~
server/src/platform/governance/automation-processing/GovernanceRuleTransformationEngine.ts:546:41 - error TS2339: Property 'enforceMemoryBoundaries' does not exist on type 'EnvironmentConstantsCalculator'.

546       await this._environmentCalculator.enforceMemoryBoundaries();
                                            ~~~~~~~~~~~~~~~~~~~~~~~
server/src/platform/governance/automation-processing/GovernanceRuleTransformationEngine.ts:958:61 - error TS2339: Property 'getSystemHealthMetrics' does not exist on type 'EnvironmentConstantsCalculator'.

958     const healthMetrics = await this._environmentCalculator.getSystemHealthMetrics();
                                                                ~~~~~~~~~~~~~~~~~~~~~~
server/src/platform/governance/automation-processing/GovernanceRuleTransformationEngine.ts:1454:64 - error TS2339: Property 'validateMemoryConstraints' does not exist on type 'EnvironmentConstantsCalculator'.

1454     const memoryValidation = await this._environmentCalculator.validateMemoryConstraints();
                                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/continuity-backup/GovernanceRuleBackupManagerContinuity.ts
ERROR: server/src/platform/governance/continuity-backup/GovernanceRuleBackupManagerContinuity.ts:343:14 - error TS2415: Class 'GovernanceRuleBackupManagerContinuity' incorrectly extends base class 'BaseTrackingService'.
  Property '_isShuttingDown' is private in type 'GovernanceRuleBackupManagerContinuity' but not in type 'BaseTrackingService'.

343 export class GovernanceRuleBackupManagerContinuity
                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
server/src/platform/governance/continuity-backup/GovernanceRuleBackupManagerContinuity.ts:420:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

420       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/continuity-backup/GovernanceRuleDisasterRecovery.ts
ERROR: server/src/platform/governance/continuity-backup/GovernanceRuleDisasterRecovery.ts:375:14 - error TS2415: Class 'GovernanceRuleDisasterRecovery' incorrectly extends base class 'BaseTrackingService'.
  Property '_isShuttingDown' is private in type 'GovernanceRuleDisasterRecovery' but not in type 'BaseTrackingService'.

375 export class GovernanceRuleDisasterRecovery
                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
server/src/platform/governance/continuity-backup/GovernanceRuleDisasterRecovery.ts:483:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

483       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/continuity-backup/GovernanceRuleFailoverManager.ts
ERROR: server/src/platform/governance/continuity-backup/GovernanceRuleFailoverManager.ts:469:14 - error TS2415: Class 'GovernanceRuleFailoverManager' incorrectly extends base class 'BaseTrackingService'.
  Property '_isShuttingDown' is private in type 'GovernanceRuleFailoverManager' but not in type 'BaseTrackingService'.

469 export class GovernanceRuleFailoverManager
                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
server/src/platform/governance/continuity-backup/GovernanceRuleFailoverManager.ts:558:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

558       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/continuity-backup/GovernanceRuleRecoveryManager.ts
ERROR: server/src/platform/governance/continuity-backup/GovernanceRuleRecoveryManager.ts:313:14 - error TS2415: Class 'GovernanceRuleRecoveryManager' incorrectly extends base class 'BaseTrackingService'.
  Property '_isShuttingDown' is private in type 'GovernanceRuleRecoveryManager' but not in type 'BaseTrackingService'.

313 export class GovernanceRuleRecoveryManager
                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
server/src/platform/governance/continuity-backup/GovernanceRuleRecoveryManager.ts:392:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

392       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/enterprise-frameworks/GovernanceRuleEnterpriseFramework.ts
ERROR: server/src/platform/governance/enterprise-frameworks/GovernanceRuleEnterpriseFramework.ts:791:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

791       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/enterprise-frameworks/GovernanceRuleGovernanceFramework.ts
ERROR: server/src/platform/governance/enterprise-frameworks/GovernanceRuleGovernanceFramework.ts:739:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

739       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/enterprise-frameworks/GovernanceRuleIntegrationFramework.ts
ERROR: server/src/platform/governance/enterprise-frameworks/GovernanceRuleIntegrationFramework.ts:271:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

271       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/enterprise-frameworks/GovernanceRuleManagementFramework.ts
ERROR: server/src/platform/governance/enterprise-frameworks/GovernanceRuleManagementFramework.ts:495:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

495       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/management-configuration/GovernanceRuleCSRFManager.ts
ERROR: server/src/platform/governance/management-configuration/GovernanceRuleCSRFManager.ts:67:11 - error TS2416: Property '_cleanupInterval' in type 'GovernanceRuleCSRFManager' is not assignable to the same property in base type 'BaseTrackingService'.
  Type 'Timeout | null' is not assignable to type 'Timeout | undefined'.
    Type 'null' is not assignable to type 'Timeout | undefined'.

67   private _cleanupInterval: NodeJS.Timeout | null = null;
             ~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/management-configuration/GovernanceRuleConfigurationManager.ts
ERROR: server/src/platform/governance/management-configuration/GovernanceRuleConfigurationManager.ts:315:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

315       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/management-configuration/GovernanceRuleTemplateEngine.ts
ERROR: server/src/platform/governance/management-configuration/GovernanceRuleTemplateEngine.ts:337:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

337       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/reporting-infrastructure/GovernanceRuleComplianceReporter.ts
ERROR: server/src/platform/governance/reporting-infrastructure/GovernanceRuleComplianceReporter.ts:483:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

483       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/reporting-infrastructure/GovernanceRuleDashboardGenerator.ts
ERROR: server/src/platform/governance/reporting-infrastructure/GovernanceRuleDashboardGenerator.ts:348:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

348       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/rule-management/RuleConflictResolutionEngine.ts
ERROR: server/src/platform/governance/rule-management/RuleConflictResolutionEngine.ts:477:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

477       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/rule-management/RuleDependencyGraphAnalyzer.ts
ERROR: server/src/platform/governance/rule-management/RuleDependencyGraphAnalyzer.ts:561:11 - error TS2345: Argument of type '{ service: { name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }; governance: { authority: string; requiredCompliance: string[]; auditFrequency: number; violationReporting: boolean; }; performance: { ...; ...' is not assignable to parameter of type 'Partial<TTrackingConfig>'.
  The types of 'service.environment' are incompatible between these types.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

561     super(DEFAULT_TRACKING_CONFIG);
              ~~~~~~~~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/rule-management/RuleExecutionContextManager.ts
ERROR: server/src/platform/governance/rule-management/RuleExecutionContextManager.ts:210:14 - error TS2415: Class 'RuleExecutionContextManager' incorrectly extends base class 'BaseTrackingService'.
  Types have separate declarations of a private property '_performPeriodicCleanup'.

210 export class RuleExecutionContextManager extends BaseTrackingService implements IGovernanceRuleExecutionContext {
                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/rule-management/RuleExecutionResultProcessor.ts
ERROR: server/src/platform/governance/rule-management/RuleExecutionResultProcessor.ts:448:7 - error TS2322: Type '{ name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }' is not assignable to type 'TServiceConfig'.
  Types of property 'environment' are incompatible.
    Type 'string' is not assignable to type '"development" | "staging" | "production"'.

448       service: {
          ~~~~~~~

  shared/src/types/platform/tracking/core/tracking-config-types.ts:66:3
    66   service: TServiceConfig;
         ~~~~~~~
    The expected type comes from property 'service' which is declared here on type 'TTrackingConfig'
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/rule-management/RuleGovernanceComplianceValidator.ts
ERROR: server/src/platform/governance/rule-management/RuleGovernanceComplianceValidator.ts:460:11 - error TS2345: Argument of type '{ service: { name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }; governance: { authority: string; requiredCompliance: string[]; auditFrequency: number; violationReporting: boolean; }; performance: { ...; ...' is not assignable to parameter of type 'Partial<TTrackingConfig>'.

460     super(DEFAULT_TRACKING_CONFIG);
              ~~~~~~~~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/rule-management/RulePerformanceOptimizationEngine.ts
ERROR: server/src/platform/governance/rule-management/RulePerformanceOptimizationEngine.ts:444:11 - error TS2345: Argument of type '{ service: { name: string; version: string; environment: string; timeout: number; retry: { maxAttempts: number; delay: number; backoffMultiplier: number; maxDelay: number; }; }; governance: { authority: string; requiredCompliance: string[]; auditFrequency: number; violationReporting: boolean; }; performance: { ...; ...' is not assignable to parameter of type 'Partial<TTrackingConfig>'.

444     super(DEFAULT_TRACKING_CONFIG);
              ~~~~~~~~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/tracking/core-trackers/GovernanceTrackingSystem.ts
ERROR: server/src/platform/tracking/core-trackers/GovernanceTrackingSystem.ts:78:10 - error TS2724: '"../../../../../shared/src/constants/platform/tracking/environment-constants-calculator"' has no exported member named 'environmentCalculator'. Did you mean 'getEnvironmentCalculator'?

78 import { environmentCalculator } from '../../../../../shared/src/constants/platform/tracking/environment-constants-calculator';
            ~~~~~~~~~~~~~~~~~~~~~

  shared/src/constants/platform/tracking/environment-constants-calculator.ts:740:17
    740 export function getEnvironmentCalculator(): EnvironmentConstantsCalculator {
                        ~~~~~~~~~~~~~~~~~~~~~~~~
    'getEnvironmentCalculator' is declared here.
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/performance-management/analytics/RulePerformanceProfiler.ts
ERROR: server/src/platform/governance/performance-management/analytics/RulePerformanceProfiler.ts:176:14 - error TS2415: Class 'RulePerformanceProfiler' incorrectly extends base class 'BaseTrackingService'.
  Types have separate declarations of a private property '_cleanupInterval'.

176 export class RulePerformanceProfiler extends BaseTrackingService implements IPerformanceService {
                 ~~~~~~~~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/performance-management/cache/RuleResourceManager.ts
ERROR: server/src/platform/governance/performance-management/cache/RuleResourceManager.ts:686:16 - error TS2416: Property 'getResourceMetrics' in type 'RuleResourceManager' is not assignable to the same property in base type 'BaseTrackingService'.
  Type '() => Promise<IResourceMetrics>' is not assignable to type '() => IResourceMetrics'.
    Type 'Promise<IResourceMetrics>' is missing the following properties from type 'IResourceMetrics': totalResources, activeIntervals, activeTimeouts, memoryUsageMB, and 2 more.

686   public async getResourceMetrics(): Promise<IResourceMetrics> {
                   ~~~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/performance-management/cache/RuleCacheManager.ts
ERROR: server/src/platform/governance/performance-management/cache/RuleCacheManager.ts:231:14 - error TS2415: Class 'RuleCacheManager' incorrectly extends base class 'BaseTrackingService'.
  Types have separate declarations of a private property '_cleanupInterval'.

231 export class RuleCacheManager extends BaseTrackingService implements ICacheManager, IPerformanceService {
                 ~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/rule-management/compliance/GovernanceAuthorityValidator.ts
ERROR: server/src/platform/governance/rule-management/compliance/GovernanceAuthorityValidator.ts:272:14 - error TS2415: Class 'GovernanceAuthorityValidator' incorrectly extends base class 'BaseTrackingService'.
  Types have separate declarations of a private property '_performPeriodicCleanup'.

272 export class GovernanceAuthorityValidator extends BaseTrackingService implements IGovernanceAuthorityValidator {
                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/rule-management/compliance/GovernanceComplianceChecker.ts
ERROR: server/src/platform/governance/rule-management/compliance/GovernanceComplianceChecker.ts:221:14 - error TS2415: Class 'GovernanceComplianceChecker' incorrectly extends base class 'BaseTrackingService'.
  Types have separate declarations of a private property '_performPeriodicCleanup'.

221 export class GovernanceComplianceChecker extends BaseTrackingService implements IGovernanceComplianceChecker {
                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/rule-management/core/GovernanceRuleEngineCore.ts
ERROR: server/src/platform/governance/rule-management/core/GovernanceRuleEngineCore.ts:207:14 - error TS2415: Class 'GovernanceRuleEngineCore' incorrectly extends base class 'BaseTrackingService'.
  Types have separate declarations of a private property '_performPeriodicCleanup'.

207 export class GovernanceRuleEngineCore extends BaseTrackingService implements IGovernanceRuleEngineCore {
                 ~~~~~~~~~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/rule-management/core/GovernanceRuleExecutionContext.ts
ERROR: server/src/platform/governance/rule-management/core/GovernanceRuleExecutionContext.ts:187:14 - error TS2415: Class 'GovernanceRuleExecutionContext' incorrectly extends base class 'BaseTrackingService'.
  Types have separate declarations of a private property '_performPeriodicCleanup'.

187 export class GovernanceRuleExecutionContext extends BaseTrackingService implements IGovernanceRuleExecutionContext {
                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/rule-management/core/GovernanceRuleValidatorFactory.ts
ERROR: server/src/platform/governance/rule-management/core/GovernanceRuleValidatorFactory.ts:280:14 - error TS2415: Class 'GovernanceRuleValidatorFactory' incorrectly extends base class 'BaseTrackingService'.
  Types have separate declarations of a private property '_performPeriodicCleanup'.

280 export class GovernanceRuleValidatorFactory extends BaseTrackingService implements IGovernanceRuleValidatorFactory {
                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/governance/rule-management/infrastructure/GovernanceRuleCacheManager.ts
ERROR: server/src/platform/governance/rule-management/infrastructure/GovernanceRuleCacheManager.ts:238:14 - error TS2415: Class 'GovernanceRuleCacheManager' incorrectly extends base class 'BaseTrackingService'.
  Types have separate declarations of a private property '_performPeriodicCleanup'.

238 export class GovernanceRuleCacheManager extends BaseTrackingService implements IGovernanceRuleCacheManager {
                 ~~~~~~~~~~~~~~~~~~~~~~~~~~
STACK:
Failed to collect coverage from /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/platform/tracking/core-trackers/security/SecurityEnforcementLayer.ts
ERROR: server/src/platform/tracking/core-trackers/security/SecurityEnforcementLayer.ts:22:10 - error TS2724: '"../../../../../../shared/src/constants/platform/tracking/environment-constants-calculator"' has no exported member named 'environmentCalculator'. Did you mean 'getEnvironmentCalculator'?

22 import { environmentCalculator } from '../../../../../../shared/src/constants/platform/tracking/environment-constants-calculator';
            ~~~~~~~~~~~~~~~~~~~~~

  shared/src/constants/platform/tracking/environment-constants-calculator.ts:740:17
    740 export function getEnvironmentCalculator(): EnvironmentConstantsCalculator {
                        ~~~~~~~~~~~~~~~~~~~~~~~~
    'getEnvironmentCalculator' is declared here.
