"use strict";
/**
 * @file Enhanced Tracking System Constants with Environment Adaptation
 * @filepath shared/src/constants/platform/tracking/tracking-constants-enhanced.ts
 * @task-id T-TSK-03.SUB-04.CONST-03
 * @component tracking-constants-enhanced
 * @reference foundation-context.CONSTANTS.003
 * @template environment-adaptive-constants
 * @tier T0
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-26
 * @modified 2025-06-26 12:25:28 +03
 *
 * @description
 * Environment-adaptive tracking system constants that automatically adjust
 * based on system resources. This enhanced version dynamically calculates
 * optimal MAX* constants while maintaining all original constants for
 * compatibility.
 *
 * Key Features:
 * - Dynamic memory-based constant calculation
 * - CPU-aware batch sizing and concurrency limits
 * - Environment-specific performance thresholds
 * - Automatic container/cloud detection
 * - Runtime recalculation capabilities
 * - Backward compatibility with original constants
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-003-adaptive-constants
 * @governance-dcr DCR-foundation-003-smart-tracking
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @enables server/src/platform/tracking/core-data
 * @requires shared/src/constants/platform/tracking/environment-constants-calculator
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, performance-optimization
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ERROR_MESSAGES = exports.VALIDATION_WARNING_CODES = exports.VALIDATION_ERROR_CODES = exports.getCurrentEnvironmentConstants = exports.isContainerized = exports.getEnvironmentMetadata = exports.forceEnvironmentRecalculation = exports.getEnvironmentCalculationSummary = exports.COMPONENT_STATUS_DESCRIPTIONS = exports.COMPONENT_STATUS_COLORS = exports.COMPONENT_STATUS_PRIORITY = exports.PERFORMANCE_THRESHOLDS = exports.CONTEXT_AUTHORITY_CONSTANTS = exports.SMART_PATH_CONSTANTS = exports.ANALYTICS_CACHE_CONSTANTS = exports.getPerformanceThresholds = exports.getContextAuthorityConstants = exports.getSmartPathConstants = exports.getAnalyticsCacheConstants = exports.LOG_ROTATION_INTERVAL = exports.MAX_LOG_RETENTION_DAYS = exports.MAX_LOG_FILE_SIZE = exports.getMaxLogRetentionDays = exports.getMaxLogFileSize = exports.DEFAULT_LOG_LEVEL = exports.MIN_BATCH_SIZE = exports.MAX_BATCH_SIZE = exports.getMinBatchSize = exports.getMaxBatchSize = exports.THROUGHPUT_THRESHOLD = exports.ERROR_RATE_THRESHOLD = exports.CPU_USAGE_THRESHOLD = exports.MEMORY_USAGE_THRESHOLD = exports.PERFORMANCE_MONITORING_INTERVAL = exports.MAX_RESPONSE_TIME = exports.getCpuUsageThreshold = exports.getMemoryUsageThreshold = exports.getPerformanceMonitoringInterval = exports.getMaxResponseTime = exports.GOVERNANCE_VALIDATION_TIMEOUT = exports.GOVERNANCE_AUDIT_FREQUENCY = exports.MAX_GOVERNANCE_VIOLATIONS = exports.MIN_COMPLIANCE_SCORE = exports.DEFAULT_AUTHORITY_LEVEL = exports.AUTHORITY_VALIDATOR = exports.DEFAULT_SERVICE_TIMEOUT = exports.MAX_TRACKING_DATA_AGE = exports.TRACKING_CACHE_TTL = exports.DEFAULT_TRACKING_INTERVAL = exports.MAX_TRACKING_RETRIES = void 0;
exports.DEFAULT_TRACKING_CONFIG = exports.getDefaultTrackingConfig = exports.AUTHORITY_LEVEL_DESCRIPTIONS = exports.AUTHORITY_LEVEL_HIERARCHY = exports.COMPONENT_PRIORITIES = exports.COMPONENT_CATEGORIES = exports.CONTEXTS = exports.MILESTONES = exports.RUNTIME_CONFIG = exports.WARNING_MESSAGES = void 0;
const environment_constants_calculator_1 = require("./environment-constants-calculator");
// ============================================================================
// STATIC CONSTANTS (Non-environment dependent)
// ============================================================================
/**
 * Maximum retry attempts for tracking operations
 */
exports.MAX_TRACKING_RETRIES = 3;
/**
 * Default tracking interval in milliseconds (5 seconds)
 */
exports.DEFAULT_TRACKING_INTERVAL = 5000;
/**
 * Tracking cache TTL in milliseconds (5 minutes)
 */
exports.TRACKING_CACHE_TTL = 300000;
/**
 * Maximum tracking data age in milliseconds (24 hours)
 */
exports.MAX_TRACKING_DATA_AGE = 86400000;
/**
 * Default service timeout in milliseconds (30 seconds)
 */
exports.DEFAULT_SERVICE_TIMEOUT = 30000;
// ============================================================================
// GOVERNANCE CONSTANTS (Static)
// ============================================================================
/**
 * Authority validator for tracking system
 */
exports.AUTHORITY_VALIDATOR = 'President & CEO, E.Z. Consultancy';
/**
 * Default authority level for tracking components
 */
exports.DEFAULT_AUTHORITY_LEVEL = 'architectural-authority';
/**
 * Minimum compliance score for governance validation
 */
exports.MIN_COMPLIANCE_SCORE = 80;
/**
 * Maximum governance violations before escalation
 */
exports.MAX_GOVERNANCE_VIOLATIONS = 5;
/**
 * Governance audit frequency in hours (24 hours)
 */
exports.GOVERNANCE_AUDIT_FREQUENCY = 24;
/**
 * Governance validation timeout in milliseconds (10 seconds)
 */
exports.GOVERNANCE_VALIDATION_TIMEOUT = 10000;
// ============================================================================
// DYNAMIC ENVIRONMENT-BASED CONSTANTS
// ============================================================================
/**
 * Get environment-calculated constants
 * This function returns fresh calculations based on current system state
 */
function getEnvironmentConstants() {
    return (0, environment_constants_calculator_1.getTrackingConstants)();
}
// ============================================================================
// PERFORMANCE CONSTANTS (Environment-adapted)
// ============================================================================
/**
 * Get maximum acceptable response time (environment-dependent)
 */
function getMaxResponseTime() {
    return getEnvironmentConstants().MAX_RESPONSE_TIME;
}
exports.getMaxResponseTime = getMaxResponseTime;
/**
 * Get performance monitoring interval (environment-dependent)
 */
function getPerformanceMonitoringInterval() {
    return getEnvironmentConstants().PERFORMANCE_MONITORING_INTERVAL;
}
exports.getPerformanceMonitoringInterval = getPerformanceMonitoringInterval;
/**
 * Get memory usage threshold in MB (environment-dependent)
 */
function getMemoryUsageThreshold() {
    return getEnvironmentConstants().MEMORY_USAGE_THRESHOLD;
}
exports.getMemoryUsageThreshold = getMemoryUsageThreshold;
/**
 * Get CPU usage threshold percentage (environment-dependent)
 */
function getCpuUsageThreshold() {
    return getEnvironmentConstants().CPU_USAGE_THRESHOLD;
}
exports.getCpuUsageThreshold = getCpuUsageThreshold;
/**
 * Static constants for backward compatibility
 */
exports.MAX_RESPONSE_TIME = getMaxResponseTime();
exports.PERFORMANCE_MONITORING_INTERVAL = getPerformanceMonitoringInterval();
exports.MEMORY_USAGE_THRESHOLD = getMemoryUsageThreshold();
exports.CPU_USAGE_THRESHOLD = getCpuUsageThreshold();
/**
 * Error rate threshold percentage (1%)
 */
exports.ERROR_RATE_THRESHOLD = 1;
/**
 * Throughput threshold (operations per second)
 */
exports.THROUGHPUT_THRESHOLD = 1000;
// ============================================================================
// BATCH PROCESSING CONSTANTS (Environment-adapted)
// ============================================================================
/**
 * Get maximum batch size for bulk operations (environment-dependent)
 */
function getMaxBatchSize() {
    return getEnvironmentConstants().MAX_BATCH_SIZE;
}
exports.getMaxBatchSize = getMaxBatchSize;
/**
 * Get minimum batch size for bulk operations (environment-dependent)
 */
function getMinBatchSize() {
    return getEnvironmentConstants().MIN_BATCH_SIZE;
}
exports.getMinBatchSize = getMinBatchSize;
/**
 * Static constants for backward compatibility
 */
exports.MAX_BATCH_SIZE = getMaxBatchSize();
exports.MIN_BATCH_SIZE = getMinBatchSize();
// ============================================================================
// LOGGING CONSTANTS (Environment-adapted)
// ============================================================================
/**
 * Default log level for tracking services
 */
exports.DEFAULT_LOG_LEVEL = 'info';
/**
 * Get maximum log file size in MB (environment-dependent)
 */
function getMaxLogFileSize() {
    return getEnvironmentConstants().MAX_LOG_FILE_SIZE;
}
exports.getMaxLogFileSize = getMaxLogFileSize;
/**
 * Get maximum log retention days (environment-dependent)
 */
function getMaxLogRetentionDays() {
    return getEnvironmentConstants().MAX_LOG_RETENTION_DAYS;
}
exports.getMaxLogRetentionDays = getMaxLogRetentionDays;
/**
 * Static constants for backward compatibility
 */
exports.MAX_LOG_FILE_SIZE = getMaxLogFileSize();
exports.MAX_LOG_RETENTION_DAYS = getMaxLogRetentionDays();
/**
 * Log rotation interval in hours (24 hours)
 */
exports.LOG_ROTATION_INTERVAL = 24;
// ============================================================================
// ENHANCED CACHE CONSTANTS (Environment-adapted)
// ============================================================================
/**
 * Get environment-optimized analytics cache constants
 */
function getAnalyticsCacheConstants() {
    return getEnvironmentConstants().ANALYTICS_CACHE_CONSTANTS;
}
exports.getAnalyticsCacheConstants = getAnalyticsCacheConstants;
/**
 * Get environment-optimized smart path constants
 */
function getSmartPathConstants() {
    return getEnvironmentConstants().SMART_PATH_CONSTANTS;
}
exports.getSmartPathConstants = getSmartPathConstants;
/**
 * Get environment-optimized context authority constants
 */
function getContextAuthorityConstants() {
    return getEnvironmentConstants().CONTEXT_AUTHORITY_CONSTANTS;
}
exports.getContextAuthorityConstants = getContextAuthorityConstants;
/**
 * Get environment-optimized performance thresholds
 */
function getPerformanceThresholds() {
    return getEnvironmentConstants().PERFORMANCE_THRESHOLDS;
}
exports.getPerformanceThresholds = getPerformanceThresholds;
/**
 * Static exports for backward compatibility
 */
exports.ANALYTICS_CACHE_CONSTANTS = getAnalyticsCacheConstants();
exports.SMART_PATH_CONSTANTS = getSmartPathConstants();
exports.CONTEXT_AUTHORITY_CONSTANTS = getContextAuthorityConstants();
exports.PERFORMANCE_THRESHOLDS = getPerformanceThresholds();
// ============================================================================
// COMPONENT STATUS CONSTANTS (Static)
// ============================================================================
/**
 * Component status priority mapping
 */
exports.COMPONENT_STATUS_PRIORITY = {
    'not-started': 0,
    'planning': 1,
    'in-progress': 2,
    'review': 3,
    'testing': 4,
    'completed': 5,
    'deployed': 6,
    'blocked': -1,
    'failed': -2
};
/**
 * Component status color mapping for UI display
 */
exports.COMPONENT_STATUS_COLORS = {
    'not-started': '#6B7280',
    'planning': '#3B82F6',
    'in-progress': '#F59E0B',
    'review': '#8B5CF6',
    'testing': '#10B981',
    'completed': '#059669',
    'deployed': '#065F46',
    'blocked': '#EF4444',
    'failed': '#DC2626'
};
/**
 * Component status descriptions for documentation
 */
exports.COMPONENT_STATUS_DESCRIPTIONS = {
    'not-started': 'Component has not been started',
    'planning': 'Component is in planning phase',
    'in-progress': 'Component is actively being developed',
    'review': 'Component is under review',
    'testing': 'Component is being tested',
    'completed': 'Component development is completed',
    'deployed': 'Component is deployed and operational',
    'blocked': 'Component is blocked by dependencies',
    'failed': 'Component has failed and needs attention'
};
// ============================================================================
// ENVIRONMENT INTROSPECTION FUNCTIONS
// ============================================================================
/**
 * Get detailed environment calculation summary
 */
function getEnvironmentCalculationSummary() {
    return (0, environment_constants_calculator_1.getEnvironmentSummary)();
}
exports.getEnvironmentCalculationSummary = getEnvironmentCalculationSummary;
/**
 * Force recalculation of environment constants
 */
function forceEnvironmentRecalculation() {
    return (0, environment_constants_calculator_1.recalculateEnvironmentConstants)();
}
exports.forceEnvironmentRecalculation = forceEnvironmentRecalculation;
/**
 * Get environment metadata including system info
 */
function getEnvironmentMetadata() {
    return getEnvironmentConstants().ENVIRONMENT_METADATA;
}
exports.getEnvironmentMetadata = getEnvironmentMetadata;
/**
 * Check if running in containerized environment
 */
function isContainerized() {
    return getEnvironmentMetadata().systemInfo.containerized;
}
exports.isContainerized = isContainerized;
/**
 * Get optimized constants for current environment
 */
function getCurrentEnvironmentConstants() {
    return getEnvironmentConstants();
}
exports.getCurrentEnvironmentConstants = getCurrentEnvironmentConstants;
// ============================================================================
// VALIDATION CONSTANTS (Static)
// ============================================================================
/**
 * Validation error codes for tracking operations
 */
exports.VALIDATION_ERROR_CODES = {
    INVALID_INPUT: 'VALIDATION_ERROR_INVALID_INPUT',
    MISSING_REQUIRED_FIELD: 'VALIDATION_ERROR_MISSING_REQUIRED_FIELD',
    INVALID_FORMAT: 'VALIDATION_ERROR_INVALID_FORMAT',
    OUT_OF_RANGE: 'VALIDATION_ERROR_OUT_OF_RANGE',
    DUPLICATE_ENTRY: 'VALIDATION_ERROR_DUPLICATE_ENTRY',
    CIRCULAR_DEPENDENCY: 'VALIDATION_ERROR_CIRCULAR_DEPENDENCY',
    AUTHORITY_INSUFFICIENT: 'VALIDATION_ERROR_AUTHORITY_INSUFFICIENT',
    SERVICE_NOT_INITIALIZED: 'VALIDATION_ERROR_SERVICE_NOT_INITIALIZED',
    INVALID_TRACKING_DATA: 'VALIDATION_ERROR_INVALID_TRACKING_DATA',
    GOVERNANCE_VIOLATION: 'VALIDATION_ERROR_GOVERNANCE_VIOLATION',
    CONFIGURATION_ERROR: 'VALIDATION_ERROR_CONFIGURATION_ERROR',
    PERFORMANCE_THRESHOLD_EXCEEDED: 'VALIDATION_ERROR_PERFORMANCE_THRESHOLD_EXCEEDED'
};
/**
 * Validation warning codes for tracking operations
 */
exports.VALIDATION_WARNING_CODES = {
    PERFORMANCE_DEGRADED: 'VALIDATION_WARNING_PERFORMANCE_DEGRADED',
    APPROACHING_LIMIT: 'VALIDATION_WARNING_APPROACHING_LIMIT',
    OUTDATED_DATA: 'VALIDATION_WARNING_OUTDATED_DATA',
    SUBOPTIMAL_CONFIGURATION: 'VALIDATION_WARNING_SUBOPTIMAL_CONFIGURATION',
    DEPENDENCY_DEPRECATED: 'VALIDATION_WARNING_DEPENDENCY_DEPRECATED',
    GOVERNANCE_RECOMMENDATION: 'VALIDATION_WARNING_GOVERNANCE_RECOMMENDATION',
    CONFIGURATION_OPTIMIZATION: 'VALIDATION_WARNING_CONFIGURATION_OPTIMIZATION',
    DEPENDENCY_UPDATE_AVAILABLE: 'VALIDATION_WARNING_DEPENDENCY_UPDATE_AVAILABLE'
};
// ============================================================================
// ERROR MESSAGES (Static)
// ============================================================================
/**
 * Error messages for tracking system operations
 */
exports.ERROR_MESSAGES = {
    TRACKING_SERVICE_UNAVAILABLE: 'Tracking service is currently unavailable',
    VALIDATION_FAILED: 'Validation failed for tracking data',
    AUTHORITY_VALIDATION_FAILED: 'Authority validation failed',
    TIMEOUT_EXCEEDED: 'Operation timeout exceeded',
    MEMORY_LIMIT_EXCEEDED: 'Memory usage limit exceeded',
    BATCH_SIZE_EXCEEDED: 'Batch size limit exceeded',
    CONCURRENT_LIMIT_EXCEEDED: 'Concurrent operation limit exceeded',
    SERVICE_NOT_INITIALIZED: 'Service has not been initialized',
    INVALID_TRACKING_DATA: 'Invalid tracking data provided',
    CONFIGURATION_ERROR: 'Configuration error encountered',
    GOVERNANCE_VIOLATION: 'Governance violation detected'
};
/**
 * Warning messages for tracking system operations
 */
exports.WARNING_MESSAGES = {
    PERFORMANCE_DEGRADED: 'System performance is degraded',
    APPROACHING_MEMORY_LIMIT: 'Approaching memory usage limit',
    HIGH_ERROR_RATE: 'Error rate is above normal threshold',
    CACHE_HIT_RATIO_LOW: 'Cache hit ratio is below optimal threshold'
};
/**
 * Runtime configuration object
 */
exports.RUNTIME_CONFIG = {
    forceRecalculation: forceEnvironmentRecalculation,
    getEnvironmentSummary: getEnvironmentCalculationSummary,
    getCurrentConstants: getCurrentEnvironmentConstants,
    isAdaptive: true,
    getLastCalculated: function () {
        return getEnvironmentMetadata().calculatedAt;
    }
};
// ============================================================================
// MILESTONES AND CONTEXTS (Static)
// ============================================================================
/**
 * OA Framework milestones
 */
exports.MILESTONES = [
    'M0', 'M1', 'M1A', 'M1B', 'M1C', 'M2', 'M2A',
    'M3', 'M4', 'M5', 'M6', 'M7', 'M7A', 'M7B',
    'M8', 'M11', 'M11A', 'M11B'
];
/**
 * OA Framework contexts
 */
exports.CONTEXTS = [
    'oa-framework',
    'foundation-context',
    'authentication-context',
    'user-experience-context',
    'production-context',
    'enterprise-context'
];
// ============================================================================
// COMPONENT CATEGORIES AND PRIORITIES (Static)
// ============================================================================
/**
 * Component categories for organization
 */
exports.COMPONENT_CATEGORIES = {
    CORE: 'core',
    INFRASTRUCTURE: 'infrastructure',
    GOVERNANCE: 'governance',
    AUTHENTICATION: 'authentication',
    USER_INTERFACE: 'user-interface',
    BUSINESS_LOGIC: 'business-logic',
    INTEGRATION: 'integration',
    MONITORING: 'monitoring'
};
/**
 * Component priorities for development planning
 */
exports.COMPONENT_PRIORITIES = {
    CRITICAL: 'critical',
    HIGH: 'high',
    MEDIUM: 'medium',
    LOW: 'low'
};
// ============================================================================
// AUTHORITY LEVEL CONSTANTS (Static)
// ============================================================================
/**
 * Authority level hierarchy for governance
 */
exports.AUTHORITY_LEVEL_HIERARCHY = {
    'low': 1,
    'standard': 2,
    'high': 3,
    'critical': 4,
    'architectural-authority': 5,
    'maximum': 6
};
/**
 * Authority level descriptions
 */
exports.AUTHORITY_LEVEL_DESCRIPTIONS = {
    'low': 'Low authority level for basic operations',
    'standard': 'Standard authority level for regular operations',
    'high': 'High authority level for important operations',
    'critical': 'Critical authority level for system-critical operations',
    'architectural-authority': 'Architectural authority for framework decisions',
    'maximum': 'Maximum authority level for emergency operations'
};
// ============================================================================
// DEFAULT CONFIGURATION (Environment-aware)
// ============================================================================
/**
 * Default tracking configuration with environment adaptation
 */
function getDefaultTrackingConfig() {
    const envConstants = getEnvironmentConstants();
    const envMetadata = getEnvironmentMetadata();
    return {
        service: {
            name: 'tracking-service',
            version: '1.0.0',
            environment: envMetadata.profile.toLowerCase(),
            timeout: exports.DEFAULT_SERVICE_TIMEOUT,
            retry: {
                maxAttempts: exports.MAX_TRACKING_RETRIES,
                delay: 1000,
                backoffMultiplier: 2,
                maxDelay: 10000
            }
        },
        governance: {
            authority: exports.AUTHORITY_VALIDATOR,
            requiredCompliance: ['authority-validation', 'audit-trail'],
            auditFrequency: exports.GOVERNANCE_AUDIT_FREQUENCY,
            violationReporting: true
        },
        performance: {
            metricsEnabled: true,
            metricsInterval: envConstants.PERFORMANCE_MONITORING_INTERVAL,
            monitoringEnabled: true,
            alertThresholds: {
                responseTime: envConstants.MAX_RESPONSE_TIME,
                errorRate: exports.ERROR_RATE_THRESHOLD,
                memoryUsage: envConstants.MEMORY_USAGE_THRESHOLD,
                cpuUsage: envConstants.CPU_USAGE_THRESHOLD
            }
        },
        logging: {
            level: exports.DEFAULT_LOG_LEVEL,
            format: 'json',
            rotation: true,
            maxFileSize: envConstants.MAX_LOG_FILE_SIZE
        },
        environment: {
            adaptive: true,
            lastCalculated: envMetadata.calculatedAt,
            systemInfo: envMetadata.systemInfo
        }
    };
}
exports.getDefaultTrackingConfig = getDefaultTrackingConfig;
/**
 * Static default configuration for backward compatibility
 */
exports.DEFAULT_TRACKING_CONFIG = getDefaultTrackingConfig();
// ============================================================================
// COMPREHENSIVE EXPORTS
// ============================================================================
exports.default = {
    // Service constants
    MAX_TRACKING_RETRIES: exports.MAX_TRACKING_RETRIES,
    DEFAULT_TRACKING_INTERVAL: exports.DEFAULT_TRACKING_INTERVAL,
    TRACKING_CACHE_TTL: exports.TRACKING_CACHE_TTL,
    MAX_TRACKING_DATA_AGE: exports.MAX_TRACKING_DATA_AGE,
    DEFAULT_SERVICE_TIMEOUT: exports.DEFAULT_SERVICE_TIMEOUT,
    // Dynamic batch constants
    getMaxBatchSize,
    getMinBatchSize,
    MAX_BATCH_SIZE: exports.MAX_BATCH_SIZE,
    MIN_BATCH_SIZE: exports.MIN_BATCH_SIZE,
    // Governance constants
    AUTHORITY_VALIDATOR: exports.AUTHORITY_VALIDATOR,
    DEFAULT_AUTHORITY_LEVEL: exports.DEFAULT_AUTHORITY_LEVEL,
    MIN_COMPLIANCE_SCORE: exports.MIN_COMPLIANCE_SCORE,
    MAX_GOVERNANCE_VIOLATIONS: exports.MAX_GOVERNANCE_VIOLATIONS,
    GOVERNANCE_AUDIT_FREQUENCY: exports.GOVERNANCE_AUDIT_FREQUENCY,
    GOVERNANCE_VALIDATION_TIMEOUT: exports.GOVERNANCE_VALIDATION_TIMEOUT,
    // Dynamic performance constants
    getMaxResponseTime,
    getPerformanceMonitoringInterval,
    getMemoryUsageThreshold,
    getCpuUsageThreshold,
    MAX_RESPONSE_TIME: exports.MAX_RESPONSE_TIME,
    PERFORMANCE_MONITORING_INTERVAL: exports.PERFORMANCE_MONITORING_INTERVAL,
    MEMORY_USAGE_THRESHOLD: exports.MEMORY_USAGE_THRESHOLD,
    CPU_USAGE_THRESHOLD: exports.CPU_USAGE_THRESHOLD,
    ERROR_RATE_THRESHOLD: exports.ERROR_RATE_THRESHOLD,
    THROUGHPUT_THRESHOLD: exports.THROUGHPUT_THRESHOLD,
    // Dynamic logging constants
    DEFAULT_LOG_LEVEL: exports.DEFAULT_LOG_LEVEL,
    getMaxLogFileSize,
    getMaxLogRetentionDays,
    MAX_LOG_FILE_SIZE: exports.MAX_LOG_FILE_SIZE,
    LOG_ROTATION_INTERVAL: exports.LOG_ROTATION_INTERVAL,
    MAX_LOG_RETENTION_DAYS: exports.MAX_LOG_RETENTION_DAYS,
    // Status constants
    COMPONENT_STATUS_PRIORITY: exports.COMPONENT_STATUS_PRIORITY,
    COMPONENT_STATUS_COLORS: exports.COMPONENT_STATUS_COLORS,
    COMPONENT_STATUS_DESCRIPTIONS: exports.COMPONENT_STATUS_DESCRIPTIONS,
    // Authority constants
    AUTHORITY_LEVEL_HIERARCHY: exports.AUTHORITY_LEVEL_HIERARCHY,
    AUTHORITY_LEVEL_DESCRIPTIONS: exports.AUTHORITY_LEVEL_DESCRIPTIONS,
    // Validation constants
    VALIDATION_ERROR_CODES: exports.VALIDATION_ERROR_CODES,
    VALIDATION_WARNING_CODES: exports.VALIDATION_WARNING_CODES,
    // Dynamic cache constants
    getAnalyticsCacheConstants,
    getSmartPathConstants,
    getContextAuthorityConstants,
    getPerformanceThresholds,
    ANALYTICS_CACHE_CONSTANTS: exports.ANALYTICS_CACHE_CONSTANTS,
    SMART_PATH_CONSTANTS: exports.SMART_PATH_CONSTANTS,
    CONTEXT_AUTHORITY_CONSTANTS: exports.CONTEXT_AUTHORITY_CONSTANTS,
    PERFORMANCE_THRESHOLDS: exports.PERFORMANCE_THRESHOLDS,
    // Configuration
    getDefaultTrackingConfig,
    DEFAULT_TRACKING_CONFIG: exports.DEFAULT_TRACKING_CONFIG,
    // Messages
    ERROR_MESSAGES: exports.ERROR_MESSAGES,
    WARNING_MESSAGES: exports.WARNING_MESSAGES,
    // Categories
    COMPONENT_CATEGORIES: exports.COMPONENT_CATEGORIES,
    COMPONENT_PRIORITIES: exports.COMPONENT_PRIORITIES,
    // Milestones and contexts
    MILESTONES: exports.MILESTONES,
    CONTEXTS: exports.CONTEXTS,
    // Environment functions
    getEnvironmentCalculationSummary,
    forceEnvironmentRecalculation,
    getEnvironmentMetadata,
    isContainerized,
    getCurrentEnvironmentConstants,
    // Runtime configuration
    RUNTIME_CONFIG: exports.RUNTIME_CONFIG
};
