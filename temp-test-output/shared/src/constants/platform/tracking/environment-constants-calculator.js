"use strict";
/**
 * @file Environment Constants Calculator
 * @filepath shared/src/constants/platform/tracking/environment-constants-calculator.ts
 * @task-id T-TSK-03.SUB-04.CONST-02
 * @component environment-constants-calculator
 * @reference foundation-context.CONSTANTS.002
 * @template intelligent-environment-adaptation
 * @tier T0
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-26
 * @modified 2025-06-26 12:25:28 +03
 *
 * @description
 * Intelligent environment constants calculator that dynamically computes optimal
 * MAX* constants and performance thresholds based on:
 * - Available system memory (RAM)
 * - CPU cores and architecture
 * - Node.js heap limits
 * - Container memory limits (Docker/Kubernetes)
 * - Available disk space
 * - Network capabilities
 * - Environment type (development/staging/production)
 *
 * Features:
 * - Memory-ratio based calculations with safety margins
 * - CPU-aware batch sizing and concurrency limits
 * - Environment-specific optimization profiles
 * - Runtime performance monitoring and auto-adjustment
 * - Container and cloud environment detection
 * - Intelligent caching strategies based on available resources
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, <PERSON><PERSON><PERSON><PERSON> Consultancy"
 * @governance-adr ADR-foundation-002-environment-adaptation
 * @governance-dcr DCR-foundation-002-smart-constants
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @enables shared/src/constants/platform/tracking/tracking-constants
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, performance-optimization
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type environment-adapter
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/constants/environment-calculator.md
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.environmentCalculator = exports.recalculateEnvironmentConstants = exports.getEnvironmentSummary = exports.getTrackingConstants = exports.getEnvironmentConstants = exports.EnvironmentConstantsCalculator = void 0;
const os = require("os");
const fs = require("fs");
// ============================================================================
// ENVIRONMENT PROFILES
// ============================================================================
const ENVIRONMENT_PROFILES = {
    development: {
        name: 'Development',
        type: 'development',
        memoryReservationRatio: 0.3,
        cpuReservationRatio: 0.5,
        cacheAllocationRatio: 0.2,
        batchSizeMultiplier: 0.5,
        concurrencyMultiplier: 0.5,
        retentionMultiplier: 0.5,
        safetyMarginRatio: 0.3 // 30% safety margin
    },
    testing: {
        name: 'Testing',
        type: 'testing',
        memoryReservationRatio: 0.2,
        cpuReservationRatio: 0.3,
        cacheAllocationRatio: 0.15,
        batchSizeMultiplier: 0.3,
        concurrencyMultiplier: 0.3,
        retentionMultiplier: 0.25,
        safetyMarginRatio: 0.4
    },
    staging: {
        name: 'Staging',
        type: 'staging',
        memoryReservationRatio: 0.6,
        cpuReservationRatio: 0.7,
        cacheAllocationRatio: 0.3,
        batchSizeMultiplier: 0.8,
        concurrencyMultiplier: 0.8,
        retentionMultiplier: 0.8,
        safetyMarginRatio: 0.2
    },
    production: {
        name: 'Production',
        type: 'production',
        memoryReservationRatio: 0.7,
        cpuReservationRatio: 0.8,
        cacheAllocationRatio: 0.4,
        batchSizeMultiplier: 1.0,
        concurrencyMultiplier: 1.0,
        retentionMultiplier: 1.0,
        safetyMarginRatio: 0.15
    }
};
// ============================================================================
// SYSTEM RESOURCE DETECTION
// ============================================================================
class EnvironmentConstantsCalculator {
    constructor() {
        this.cachedResources = null;
        this.lastCalculation = null;
        this.recalculationInterval = null;
        this.currentNodeEnv = null;
        // Set up automatic recalculation every 5 minutes
        this.recalculationInterval = setInterval(() => {
            this.invalidateCache();
        }, 5 * 60 * 1000);
    }
    static getInstance() {
        if (!EnvironmentConstantsCalculator.instance) {
            EnvironmentConstantsCalculator.instance = new EnvironmentConstantsCalculator();
        }
        return EnvironmentConstantsCalculator.instance;
    }
    /**
     * Get current system resources
     */
    getSystemResources() {
        if (this.cachedResources) {
            return this.cachedResources;
        }
        const totalMemoryBytes = os.totalmem();
        const freeMemoryBytes = os.freemem();
        const totalCPUCores = os.cpus().length;
        // Get Node.js heap limit
        const v8 = require('v8');
        const heapStats = v8.getHeapStatistics();
        const nodeHeapLimitMB = Math.round(heapStats.heap_size_limit / (1024 * 1024));
        // Detect container memory limit (Docker/Kubernetes)
        const containerMemoryLimitMB = this.detectContainerMemoryLimit();
        // Get available disk space
        const availableDiskSpaceMB = this.getAvailableDiskSpace();
        // Build the resources object with conditional containerMemoryLimitMB
        const resourcesBase = {
            totalMemoryMB: Math.round(totalMemoryBytes / (1024 * 1024)),
            freeMemoryMB: Math.round(freeMemoryBytes / (1024 * 1024)),
            totalCPUCores,
            nodeHeapLimitMB,
            availableDiskSpaceMB,
            platform: os.platform(),
            architecture: os.arch(),
            nodeVersion: process.version
        };
        this.cachedResources = containerMemoryLimitMB !== undefined
            ? { ...resourcesBase, containerMemoryLimitMB }
            : resourcesBase;
        return this.cachedResources;
    }
    /**
     * Detect container memory limits (Docker/Kubernetes)
     */
    detectContainerMemoryLimit() {
        try {
            // Check for cgroup memory limit (Docker/Kubernetes)
            const cgroupPath = '/sys/fs/cgroup/memory/memory.limit_in_bytes';
            if (fs.existsSync(cgroupPath)) {
                const limitBytes = parseInt(fs.readFileSync(cgroupPath, 'utf8').trim());
                // If limit is unreasonably high, it's probably not a real container limit
                if (limitBytes < 9223372036854775807) { // Max int64 - 1
                    return Math.round(limitBytes / (1024 * 1024));
                }
            }
            // Check for cgroup v2 memory limit
            const cgroupV2Path = '/sys/fs/cgroup/memory.max';
            if (fs.existsSync(cgroupV2Path)) {
                const limitStr = fs.readFileSync(cgroupV2Path, 'utf8').trim();
                if (limitStr !== 'max') {
                    const limitBytes = parseInt(limitStr);
                    return Math.round(limitBytes / (1024 * 1024));
                }
            }
        }
        catch (error) {
            // Ignore errors - we're running on a system without cgroup
        }
        return undefined;
    }
    /**
     * Get available disk space
     */
    getAvailableDiskSpace() {
        try {
            const stats = fs.statSync(process.cwd());
            // This is a simplified approach - in production you might want to use statvfs
            return 10000; // Default 10GB - replace with actual disk space detection
        }
        catch (error) {
            return 10000; // Default fallback
        }
    }
    /**
     * Determine environment profile based on NODE_ENV and system characteristics
     */
    getEnvironmentProfile() {
        const nodeEnv = process.env.NODE_ENV || 'development';
        // Map NODE_ENV to our profiles
        if (nodeEnv === 'test')
            return ENVIRONMENT_PROFILES.testing;
        if (nodeEnv === 'staging')
            return ENVIRONMENT_PROFILES.staging;
        if (nodeEnv === 'production')
            return ENVIRONMENT_PROFILES.production;
        return ENVIRONMENT_PROFILES.development;
    }
    /**
     * Calculate optimal constants based on system resources and environment
     */
    calculateConstants() {
        const nodeEnv = process.env.NODE_ENV || 'development';
        // Invalidate cache if environment changed
        if (this.currentNodeEnv !== nodeEnv) {
            this.invalidateCache();
            this.currentNodeEnv = nodeEnv;
        }
        if (this.lastCalculation) {
            return this.lastCalculation;
        }
        const resources = this.getSystemResources();
        const profile = this.getEnvironmentProfile();
        // Determine effective memory limit (container limit takes precedence)
        const effectiveMemoryMB = resources.containerMemoryLimitMB ||
            Math.min(resources.totalMemoryMB, resources.nodeHeapLimitMB * 4);
        // Calculate memory allocation with strict limits
        const reservedMemoryMB = Math.floor(effectiveMemoryMB * profile.memoryReservationRatio);
        const safeMemoryMB = Math.floor(reservedMemoryMB * (1 - profile.safetyMarginRatio));
        const cacheMemoryMB = Math.floor(safeMemoryMB * profile.cacheAllocationRatio);
        // Calculate CPU-based constants
        const effectiveCores = Math.max(1, Math.floor(resources.totalCPUCores * profile.cpuReservationRatio));
        // Base constants (minimum values)
        const baseConstants = {
            minBatchSize: 10,
            minCacheSize: 5,
            minConcurrency: 2,
            minRetention: 7
        };
        // Environment-specific response times
        const responseTime = profile.type === 'production' ? 100 : 500;
        // Calculate cache distribution with strict size limits
        const maxCacheSize = Math.floor(Math.min(cacheMemoryMB * 0.8, // Max 80% of cache memory
        safeMemoryMB * 0.4 // Max 40% of safe memory
        ));
        // Ensure cache sizes are properly scaled and proportional
        const analyticsCacheSize = Math.max(baseConstants.minCacheSize + 5, // Ensure it's larger than other caches
        Math.min(Math.floor(maxCacheSize * 0.4), // 40% of max cache
        Math.floor(safeMemoryMB * 0.2) // 20% of safe memory
        ));
        const smartPathCacheSize = Math.max(baseConstants.minCacheSize, Math.min(analyticsCacheSize - 1, // Must be less than analytics
        Math.floor(maxCacheSize * 0.3) // 30% of max cache
        ));
        const contextAuthCacheSize = Math.max(baseConstants.minCacheSize, Math.min(smartPathCacheSize - 1, // Must be less than smart path
        Math.floor(maxCacheSize * 0.2) // 20% of max cache
        ));
        // Environment-specific cache TTL
        const cacheTTL = profile.type === 'production' ? 3600000 : // 1 hour
            profile.type === 'staging' ? 1800000 : // 30 minutes
                300000; // 5 minutes for dev/test
        this.lastCalculation = {
            // Memory-based constants
            MAX_MEMORY_USAGE: safeMemoryMB * 1024 * 1024,
            MEMORY_USAGE_THRESHOLD: Math.floor(safeMemoryMB * 0.8),
            // Cache constants (distributed across different cache types)
            MAX_CACHE_SIZE: maxCacheSize,
            ANALYTICS_CACHE_MAX_SIZE: analyticsCacheSize,
            SMART_PATH_CACHE_SIZE: smartPathCacheSize,
            CONTEXT_AUTHORITY_CACHE_SIZE: contextAuthCacheSize,
            // Batch processing constants (CPU and memory based)
            MAX_BATCH_SIZE: Math.max(baseConstants.minBatchSize, Math.floor(effectiveCores * 50 * profile.batchSizeMultiplier)),
            MIN_BATCH_SIZE: Math.max(1, Math.floor(baseConstants.minBatchSize * 0.1)),
            MAX_METRICS_BATCH_SIZE: Math.max(100, Math.floor(effectiveCores * 200 * profile.batchSizeMultiplier)),
            WARMUP_BATCH_SIZE: Math.max(5, Math.floor(effectiveCores * 5)),
            // Concurrency constants (CPU based)
            MAX_CONCURRENT_OPERATIONS: Math.max(baseConstants.minConcurrency, Math.floor(effectiveCores * 4 * profile.concurrencyMultiplier)),
            MAX_REALTIME_SUBSCRIBERS: Math.max(10, Math.floor(effectiveCores * 25 * profile.concurrencyMultiplier)),
            MAX_CONCURRENT_OPTIMIZATIONS: Math.max(2, Math.floor(effectiveCores * 2 * profile.concurrencyMultiplier)),
            // Performance thresholds (environment dependent)
            MAX_RESPONSE_TIME: responseTime,
            PERFORMANCE_MONITORING_INTERVAL: profile.type === 'production' ? 30000 : 60000,
            CPU_USAGE_THRESHOLD: profile.type === 'production' ? 70 : 80,
            // Retention and cleanup constants
            MAX_LOG_FILE_SIZE: Math.max(5, Math.floor(10 * profile.retentionMultiplier)),
            MAX_LOG_RETENTION_DAYS: Math.max(baseConstants.minRetention, Math.floor(30 * profile.retentionMultiplier)),
            CACHE_TTL: cacheTTL,
            CLEANUP_INTERVAL: profile.type === 'production' ? 300000 : 600000,
            // Query and processing limits
            MAX_QUERY_SIZE: Math.min(50 * 1024 * 1024, // Cap at 50MB
            Math.floor(safeMemoryMB * 0.1 * 1024 * 1024)),
            MAX_DEPENDENCY_DEPTH: profile.type === 'production' ? 12 : 8,
            MAX_AUTHORITY_CHAIN_DEPTH: profile.type === 'production' ? 8 : 5,
            // Environment metadata
            calculatedAt: new Date(),
            environmentProfile: profile.name,
            systemResources: resources
        };
        return this.lastCalculation;
    }
    /**
     * Get constants formatted for direct use in tracking-constants.ts
     */
    getTrackingConstants() {
        const calculated = this.calculateConstants();
        return {
            // Original constants that should be dynamically calculated
            MEMORY_USAGE_THRESHOLD: calculated.MEMORY_USAGE_THRESHOLD,
            MAX_BATCH_SIZE: calculated.MAX_BATCH_SIZE,
            MIN_BATCH_SIZE: calculated.MIN_BATCH_SIZE,
            MAX_RESPONSE_TIME: calculated.MAX_RESPONSE_TIME,
            PERFORMANCE_MONITORING_INTERVAL: calculated.PERFORMANCE_MONITORING_INTERVAL,
            CPU_USAGE_THRESHOLD: calculated.CPU_USAGE_THRESHOLD,
            MAX_LOG_FILE_SIZE: calculated.MAX_LOG_FILE_SIZE,
            MAX_LOG_RETENTION_DAYS: calculated.MAX_LOG_RETENTION_DAYS,
            // Enhanced constants for new caching systems
            ANALYTICS_CACHE_CONSTANTS: {
                DEFAULT_MAX_SIZE: calculated.ANALYTICS_CACHE_MAX_SIZE,
                DEFAULT_TTL: calculated.CACHE_TTL,
                COMPRESSION_THRESHOLD: 1024 * 1024,
                CLEANUP_INTERVAL: calculated.CLEANUP_INTERVAL,
                WARMUP_BATCH_SIZE: calculated.WARMUP_BATCH_SIZE,
                MAX_QUERY_SIZE: calculated.MAX_QUERY_SIZE,
            },
            SMART_PATH_CONSTANTS: {
                DEFAULT_CACHE_SIZE: calculated.SMART_PATH_CACHE_SIZE,
                PATH_TTL: calculated.CACHE_TTL / 2,
                OPTIMIZATION_INTERVAL: calculated.CLEANUP_INTERVAL * 2,
                MAX_PATH_DEPTH: calculated.MAX_DEPENDENCY_DEPTH,
                MIN_CONFIDENCE_THRESHOLD: 0.7,
                ANALYSIS_TIMEOUT: calculated.MAX_RESPONSE_TIME * 50,
            },
            CONTEXT_AUTHORITY_CONSTANTS: {
                DEFAULT_CACHE_SIZE: calculated.CONTEXT_AUTHORITY_CACHE_SIZE,
                DEFAULT_TTL: calculated.CACHE_TTL,
                CLEANUP_INTERVAL: calculated.CLEANUP_INTERVAL,
                MAX_HISTORY_SIZE: calculated.CONTEXT_AUTHORITY_CACHE_SIZE * 2,
                VALIDATION_TIMEOUT: calculated.MAX_RESPONSE_TIME * 50,
                AUTHORITY_CHAIN_MAX_DEPTH: calculated.MAX_AUTHORITY_CHAIN_DEPTH,
            },
            PERFORMANCE_THRESHOLDS: {
                MAX_RESPONSE_TIME: calculated.MAX_RESPONSE_TIME * 10,
                MAX_MEMORY_USAGE: calculated.MAX_MEMORY_USAGE,
                MAX_CPU_USAGE: calculated.CPU_USAGE_THRESHOLD,
                MIN_CACHE_HIT_RATIO: 0.7,
                MAX_ERROR_RATE: 0.05,
                MIN_THROUGHPUT: Math.floor(calculated.MAX_CONCURRENT_OPERATIONS * 10),
            },
            // Runtime metadata
            ENVIRONMENT_METADATA: {
                calculatedAt: calculated.calculatedAt.toISOString(),
                profile: calculated.environmentProfile,
                systemInfo: {
                    totalMemoryMB: calculated.systemResources.totalMemoryMB,
                    totalCPUCores: calculated.systemResources.totalCPUCores,
                    platform: calculated.systemResources.platform,
                    nodeVersion: calculated.systemResources.nodeVersion,
                    containerized: calculated.systemResources.containerMemoryLimitMB !== undefined
                }
            }
        };
    }
    /**
     * Force recalculation on next request
     */
    invalidateCache() {
        this.cachedResources = null;
        this.lastCalculation = null;
    }
    /**
     * Get human-readable summary of calculated constants
     */
    getCalculationSummary() {
        const calculated = this.calculateConstants();
        const resources = calculated.systemResources;
        return `
🎯 Environment Constants Calculator Summary
==========================================
📅 Calculated: ${calculated.calculatedAt.toISOString()}
🏷️  Profile: ${calculated.environmentProfile}

💾 System Resources:
   • Total Memory: ${resources.totalMemoryMB}MB
   • CPU Cores: ${resources.totalCPUCores}
   • Node Heap Limit: ${resources.nodeHeapLimitMB}MB
   • Container Limit: ${resources.containerMemoryLimitMB || 'None'}MB
   • Platform: ${resources.platform} (${resources.architecture})

🚀 Calculated Constants:
   • Memory Threshold: ${calculated.MEMORY_USAGE_THRESHOLD}MB
   • Max Batch Size: ${calculated.MAX_BATCH_SIZE}
   • Max Concurrent Ops: ${calculated.MAX_CONCURRENT_OPERATIONS}
   • Cache Size: ${calculated.MAX_CACHE_SIZE}
   • Max Response Time: ${calculated.MAX_RESPONSE_TIME}ms
   • CPU Threshold: ${calculated.CPU_USAGE_THRESHOLD}%

📊 Cache Allocation:
   • Analytics Cache: ${calculated.ANALYTICS_CACHE_MAX_SIZE}
   • Smart Path Cache: ${calculated.SMART_PATH_CACHE_SIZE}
   • Authority Cache: ${calculated.CONTEXT_AUTHORITY_CACHE_SIZE}
   • Total Cache Memory: ~${calculated.ANALYTICS_CACHE_MAX_SIZE + calculated.SMART_PATH_CACHE_SIZE + calculated.CONTEXT_AUTHORITY_CACHE_SIZE}MB
`;
    }
    /**
     * Enforce memory boundaries for M0 governance integration
     */
    async enforceMemoryBoundaries() {
        const resources = this.getSystemResources();
        const memoryUsage = (process.memoryUsage().heapUsed / 1024 / 1024);
        const memoryLimit = resources.totalMemoryMB * 0.8; // 80% threshold
        if (memoryUsage > memoryLimit) {
            // Trigger garbage collection if available
            if (global.gc) {
                global.gc();
            }
            // Log memory boundary enforcement
            console.warn(`Memory boundary enforced: ${memoryUsage.toFixed(2)}MB > ${memoryLimit.toFixed(2)}MB`);
        }
    }
    /**
     * Get system health metrics for M0 governance integration
     */
    async getSystemHealthMetrics() {
        const resources = this.getSystemResources();
        const memoryUsage = process.memoryUsage();
        return {
            memory: {
                used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
                total: resources.totalMemoryMB,
                free: resources.freeMemoryMB,
                usage: Math.round((memoryUsage.heapUsed / 1024 / 1024 / resources.totalMemoryMB) * 100)
            },
            cpu: {
                cores: resources.totalCPUCores,
                load: os.loadavg()[0],
                architecture: resources.architecture
            },
            uptime: Math.round(process.uptime()),
            timestamp: new Date().toISOString()
        };
    }
    /**
     * Validate memory constraints for M0 governance integration
     */
    async validateMemoryConstraints() {
        const resources = this.getSystemResources();
        const memoryUsage = process.memoryUsage();
        const usedMemoryMB = memoryUsage.heapUsed / 1024 / 1024;
        const thresholdMB = resources.totalMemoryMB * 0.8;
        return {
            valid: usedMemoryMB <= thresholdMB,
            usedMemoryMB: Math.round(usedMemoryMB),
            thresholdMB: Math.round(thresholdMB),
            availableMemoryMB: Math.round(resources.freeMemoryMB),
            message: usedMemoryMB > thresholdMB ? 'Memory usage exceeds threshold' : 'Memory usage within limits'
        };
    }
    /**
     * Cleanup resources
     */
    destroy() {
        if (this.recalculationInterval) {
            clearInterval(this.recalculationInterval);
            this.recalculationInterval = null;
        }
    }
}
exports.EnvironmentConstantsCalculator = EnvironmentConstantsCalculator;
// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================
/**
 * Get singleton instance and calculate constants immediately
 */
function getEnvironmentConstants() {
    return EnvironmentConstantsCalculator.getInstance().calculateConstants();
}
exports.getEnvironmentConstants = getEnvironmentConstants;
/**
 * Get constants formatted for tracking-constants.ts integration
 */
function getTrackingConstants() {
    return EnvironmentConstantsCalculator.getInstance().getTrackingConstants();
}
exports.getTrackingConstants = getTrackingConstants;
/**
 * Get environment calculation summary
 */
function getEnvironmentSummary() {
    return EnvironmentConstantsCalculator.getInstance().getCalculationSummary();
}
exports.getEnvironmentSummary = getEnvironmentSummary;
/**
 * Force recalculation of environment constants
 */
function recalculateEnvironmentConstants() {
    const calculator = EnvironmentConstantsCalculator.getInstance();
    calculator.invalidateCache();
    return calculator.calculateConstants();
}
exports.recalculateEnvironmentConstants = recalculateEnvironmentConstants;
// ============================================================================
// EXPORTS
// ============================================================================
exports.default = EnvironmentConstantsCalculator;
// Export singleton instance for immediate use
exports.environmentCalculator = EnvironmentConstantsCalculator.getInstance();
