#!/bin/bash

# Initialize counters
test_count=0
implementation_count=0

# Create/clear the archive files
echo "Creating TypeScript archives..."
echo "" > oa-tests-archive.txt
echo "" > oa-implementation-archive.txt

# Function to check if a file is a test file
is_test_file() {
    local file="$1"
    # Check if file matches test patterns
    if [[ "$file" == *".test.ts" ]] || [[ "$file" == *".test.tsx" ]] || \
       [[ "$file" == *".spec.ts" ]] || [[ "$file" == *".spec.tsx" ]] || \
       [[ "$file" == *"__tests__"* ]] || [[ "$file" == *"/test/"* ]] || \
       [[ "$file" == *"/tests/"* ]]; then
        return 0  # true
    else
        return 1  # false
    fi
}

# Function to archive a file to the appropriate archive
archive_file() {
    local file="$1"
    local archive_file="$2"
    
    echo "" >> "$archive_file"
    echo "============================================" >> "$archive_file"
    echo "File: $file" >> "$archive_file"
    echo "============================================" >> "$archive_file"
    echo "" >> "$archive_file"
    cat "$file" >> "$archive_file"
    echo "" >> "$archive_file"
    echo "" >> "$archive_file"
}

# Process each directory
for dir in "server" "shared" "client"; do
    if [ -d "$dir" ]; then
        echo "Processing $dir directory..."
        
        # Find all TypeScript files in the directory
        find "$dir" -name "*.ts" -o -name "*.tsx" | while read -r file; do
            if is_test_file "$file"; then
                archive_file "$file" "oa-tests-archive.txt"
            else
                archive_file "$file" "oa-implementation-archive.txt"
            fi
        done
    else
        echo "Warning: $dir directory not found"
    fi
done

# Count files for summary
for dir in "server" "shared" "client"; do
    if [ -d "$dir" ]; then
        # Count test files
        while read -r file; do
            if is_test_file "$file"; then
                ((test_count++))
            else
                ((implementation_count++))
            fi
        done < <(find "$dir" -name "*.ts" -o -name "*.tsx")
    fi
done

echo ""
echo "Archives created successfully:"
echo "- Test files: oa-tests-archive.txt"
echo "- Implementation files: oa-implementation-archive.txt"
echo ""

# Display summary
total_count=$((test_count + implementation_count))

echo "Summary:"
echo "Test files: $test_count"
echo "Implementation files: $implementation_count"
echo "Total files archived: $total_count"

echo ""
echo "Test file patterns detected:"
echo "- *.test.ts, *.test.tsx"
echo "- *.spec.ts, *.spec.tsx" 
echo "- Files in __tests__ directories"
echo "- Files in test/tests directories"
