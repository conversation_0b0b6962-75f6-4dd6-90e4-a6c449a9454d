// Jest setup file for test environment configuration

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error';

// CRITICAL FIX: Global timer mocking to prevent ANY real timer creation (Lesson 04 pattern)
const mockSetInterval = jest.fn(() => 'mock-global-interval-id');
const mockClearInterval = jest.fn();
const mockSetTimeout = jest.fn(() => 'mock-global-timeout-id');
const mockClearTimeout = jest.fn();

// Override global timer functions BEFORE any modules load
global.setInterval = mockSetInterval;
global.clearInterval = mockClearInterval;
global.setTimeout = mockSetTimeout;
global.clearTimeout = mockClearTimeout;

console.log('[JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED');

// CRITICAL FIX: Global module mocking to prevent resource allocation during imports (Lesson 04 pattern)
jest.doMock('shared/src/base/MemorySafeResourceManager', () => {
  console.log('[JEST SETUP] Mocking MemorySafeResourceManager module - NO RESOURCES WILL BE CREATED');

  class MockMemorySafeResourceManager {
    constructor() {
      console.log('[JEST SETUP MOCK] MemorySafeResourceManager constructor - NO SIDE EFFECTS');
    }

    async initialize() {
      console.log('[JEST SETUP MOCK] MemorySafeResourceManager.initialize() - NO INTERVALS CREATED');
    }

    async doInitialize() {
      console.log('[JEST SETUP MOCK] MemorySafeResourceManager.doInitialize() - NO INTERVALS CREATED');
    }

    async shutdown() {
      console.log('[JEST SETUP MOCK] MemorySafeResourceManager.shutdown() - MOCKED');
    }

    async doShutdown() {
      console.log('[JEST SETUP MOCK] MemorySafeResourceManager.doShutdown() - MOCKED');
    }

    isHealthy() { return true; }
    isShuttingDown() { return false; }
    getResourceMetrics() { return { totalResources: 0 }; }

    createSafeInterval() {
      console.log('[JEST SETUP MOCK] createSafeInterval() called - MOCKED, NO REAL INTERVAL');
      return 'mock-interval-id';
    }

    createSafeTimeout() {
      console.log('[JEST SETUP MOCK] createSafeTimeout() called - MOCKED, NO REAL TIMEOUT');
      return 'mock-timeout-id';
    }

    static forceGlobalCleanup() {
      console.log('[JEST SETUP MOCK] MemorySafeResourceManager.forceGlobalCleanup() - MOCKED');
    }
  }

  return { MemorySafeResourceManager: MockMemorySafeResourceManager };
});

// CRITICAL FIX: Mock LoggingMixin to prevent console operations (Lesson 04 pattern)
jest.doMock('shared/src/base/LoggingMixin', () => {
  console.log('[JEST SETUP] Mocking LoggingMixin module');

  return {
    SimpleLogger: class MockSimpleLogger {
      constructor() {
        console.log('[JEST SETUP MOCK] SimpleLogger constructor - NO SIDE EFFECTS');
      }
      logInfo() {}
      logWarning() {}
      logError() {}
      logDebug() {}
    }
  };
});

console.log('[JEST SETUP] All critical modules mocked at global level');

// CRITICAL FIX: Remove global fake timers to prevent memory leaks
// Individual tests should control their own timer mocking
// jest.useFakeTimers(); // REMOVED - causes massive memory retention

// Global test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Setup and teardown for each test
beforeEach(() => {
  jest.clearAllMocks();
});

afterEach(() => {
  jest.clearAllTimers();
});

// Global test utilities
global.testUtils = {
  delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  createMockDate: (dateString) => new Date(dateString),
  generateUUID: () => 'test-uuid-' + Math.random().toString(36).substring(2, 11),
}; 