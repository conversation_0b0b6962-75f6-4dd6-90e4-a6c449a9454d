{"name": "oa-framework", "version": "1.0.0", "description": "Open Architecture Framework - Enterprise-grade component tracking and governance system", "main": "server/src/index.ts", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "JEST_VERBOSE=true jest --verbose", "test:debug": "DEBUG_TESTS=true jest --verbose --no-cache", "test:orchestration": "jest server/src/platform/tracking/advanced-data/__tests__/OrchestrationCoordinator.test.ts", "test:orchestration:watch": "jest server/src/platform/tracking/advanced-data/__tests__/OrchestrationCoordinator.test.ts --watch", "test:orchestration:coverage": "jest server/src/platform/tracking/advanced-data/__tests__/OrchestrationCoordinator.test.ts --coverage", "test:orchestration:debug": "DEBUG_TESTS=true jest server/src/platform/tracking/advanced-data/__tests__/OrchestrationCoordinator.test.ts --verbose --no-cache", "test:orchestration:performance": "jest server/src/platform/tracking/advanced-data/__tests__/OrchestrationCoordinator.test.ts --testNamePattern=\"Performance Benchmarks\"", "test:implementation-tracker": "jest tests/platform/tracking/core-data/ImplementationProgressTracker", "test:T-TSK-01": "jest tests/platform/tracking/core-data/", "test:T-TSK-02": "jest tests/platform/tracking/core-trackers/", "test:G-TSK-01": "jest tests/platform/governance/rule-management/core/", "test:G-TSK-02": "jest tests/platform/governance/rule-management/", "test:m0-components": "jest tests/platform/", "test:unit": "jest --testPathPattern=\".*\\.test\\.ts$\" --testPathIgnorePatterns=\".*integration.*\"", "test:integration": "jest --testPathPattern=\".*integration.*\\.test\\.ts$\"", "test:e2e": "jest --testPathPattern=\".*e2e.*\\.test\\.ts$\"", "test:ci": "jest --ci --coverage --watchAll=false --passWithNoTests", "test:ci:orchestration": "jest server/src/platform/tracking/advanced-data/__tests__/OrchestrationCoordinator.test.ts --ci --coverage --watchAll=false", "test:memory": "jest --logHeapUsage --detectLeaks --detectOpenHandles", "test:performance": "jest --testNamePattern=\"Performance|Benchmark\" --verbose", "build": "tsc", "build:watch": "tsc --watch", "build:production": "tsc --sourceMap false --removeComments true", "dev": "ts-node server/src/index.ts", "dev:watch": "ts-node --watch server/src/index.ts", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "lint:orchestration": "eslint server/src/platform/tracking/advanced-data/__tests__/OrchestrationCoordinator.test.ts --ext .ts", "typecheck": "tsc --noEmit", "typecheck:watch": "tsc --noEmit --watch", "typecheck:orchestration": "tsc server/src/platform/tracking/advanced-data/__tests__/OrchestrationCoordinator.test.ts --noEmit", "clean": "rimraf dist coverage .jest-cache", "clean:all": "rimraf dist coverage .jest-cache node_modules", "pretest": "npm run typecheck", "pretest:orchestration": "npm run typecheck:orchestration", "posttest": "npm run test:coverage", "docs": "typedoc --out docs server/src", "docs:orchestration": "typedoc --out docs/orchestration server/src/platform/tracking/advanced-data/OrchestrationCoordinator.ts"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/lodash": "^4.14.202", "@types/node": "^20.11.0", "@types/reflect-metadata": "^0.1.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "jest": "^29.7.0", "jest-junit": "^16.0.0", "jest-watch-typeahead": "^2.2.2", "rimraf": "^5.0.10", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typedoc": "^0.25.13", "typescript": "^5.3.3"}, "dependencies": {"inversify": "^7.5.4", "lodash": "^4.17.21", "reflect-metadata": "^0.2.2", "tslib": "^2.6.2", "uuid": "^9.0.1"}, "keywords": ["framework", "tracking", "governance", "enterprise", "typescript", "architecture", "orchestration", "coordination", "workflow", "testing"], "author": "E.Z. Consultancy", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ez-consultancy/oa-framework.git"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "jest": {"projects": [{"displayName": "unit", "testMatch": ["<rootDir>/**/__tests__/**/*.test.ts"], "testPathIgnorePatterns": [".*integration.*", ".*e2e.*"]}, {"displayName": "integration", "testMatch": ["<rootDir>/**/*integration*.test.ts"]}, {"displayName": "orchestration", "testMatch": ["<rootDir>/server/src/platform/tracking/advanced-data/__tests__/OrchestrationCoordinator.test.ts"]}]}, "eslintConfig": {"extends": ["@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}