
> oa-framework@1.0.0 test
> jest tests/shared/constants/tracking-constants-enhanced.test.ts

ts-jest[ts-jest-transformer] (WARN) Define `ts-jest` config under `globals` is deprecated. Please do
transform: {
    <transform_regex>: ['ts-jest', { /* ts-jest config goes here in Jest */ }],
},
See more at https://kulshekhar.github.io/ts-jest/docs/getting-started/presets#advanced
FAIL tests/shared/constants/tracking-constants-enhanced.test.ts (31.766 s)
  Enhanced Tracking Constants - Core Functionality Tests
    Static Constants Validation
      ✓ should have correct static tracking constants (3 ms)
      ✓ should have correct validation error codes (1 ms)
      ✓ should have correct warning messages
      ✓ should have correct error messages
      ✓ should have correct default configuration values (1 ms)
    Environment-Based Constants
      ✓ should adapt response time based on environment (1 ms)
      ✓ should adapt monitoring interval based on environment (2 ms)
      ✓ should provide environment-specific analytics cache constants (1 ms)
      ✓ should adapt performance thresholds based on environment (1 ms)
    Runtime Configuration Management
      ✓ should provide access to current environment constants (1 ms)
      ✓ should allow force recalculation of constants (1 ms)
      ✓ should provide environment calculation summary (1 ms)
      ✓ should detect containerized environment (1 ms)
      ✓ should provide environment metadata (1 ms)
    Default Tracking Configuration
      ✓ should provide complete tracking configuration (1 ms)
      ✓ should include retry configuration (1 ms)
      ✓ should include performance thresholds
      ✓ should include logging configuration
    Runtime Configuration Interface
      ✓ should provide runtime configuration interface
      ✕ should allow runtime recalculation (30002 ms)
      ✓ should provide current constants (2 ms)
      ✓ should indicate adaptive status

  ● Enhanced Tracking Constants - Core Functionality Tests › Runtime Configuration Interface › should allow runtime recalculation

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      227 |     });
      228 |
    > 229 |     test('should allow runtime recalculation', async () => {
          |     ^
      230 |       const before = RUNTIME_CONFIG.getLastCalculated();
      231 |       
      232 |       // Add a small delay to ensure timestamp difference

      at tests/shared/constants/tracking-constants-enhanced.test.ts:229:5
      at tests/shared/constants/tracking-constants-enhanced.test.ts:220:3
      at Object.<anonymous> (tests/shared/constants/tracking-constants-enhanced.test.ts:46:1)

