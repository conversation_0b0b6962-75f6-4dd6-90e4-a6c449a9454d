/**
 * @file Notification Interfaces
 * @description Interface definitions for notification system components
 */

import {
  TNotificationConfig,
  TNotificationChannel,
  TNotificationTemplate,
  TNotificationEvent,
  TNotificationResult,
  TNotificationStatus
} from '../../types/platform/governance/notification-types';

/**
 * Rule notification system interface
 * Manages rule-based notifications and alerts
 */
export interface IGovernanceRuleNotificationSystem {
  /**
   * Send a notification
   * @param event - Notification event
   * @param config - Notification configuration
   * @param accessToken - Optional access token
   */
  sendNotification(
    event: TNotificationEvent,
    config: TNotificationConfig,
    accessToken?: string
  ): Promise<TNotificationResult>;

  /**
   * Update notification template
   * @param template - Notification template
   * @param accessToken - Optional access token
   */
  updateTemplate(
    template: TNotificationTemplate,
    accessToken?: string
  ): Promise<void>;

  /**
   * Configure notification channel
   * @param channel - Notification channel
   * @param accessToken - Optional access token
   */
  configureChannel(
    channel: TNotificationChannel,
    accessToken?: string
  ): Promise<void>;

  /**
   * Get notification status
   * @param eventId - Event identifier
   * @param accessToken - Optional access token
   */
  getNotificationStatus(
    eventId: string,
    accessToken?: string
  ): Promise<TNotificationStatus>;
} 