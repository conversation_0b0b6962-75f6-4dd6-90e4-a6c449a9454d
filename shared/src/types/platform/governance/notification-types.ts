/**
 * @file Notification Types
 * @description Type definitions for notification system components
 */

/**
 * Notification configuration type
 */
export type TNotificationConfig = {
  channels: TNotificationChannel[];
  recipients: TNotificationRecipient[];
  template?: string;
  priority: TNotificationPriority;
  metadata: Record<string, any>;
};

/**
 * Notification channel type
 */
export type TNotificationChannel = {
  channelId: string;
  type: 'email' | 'sms' | 'slack' | 'teams' | 'webhook';
  config: Record<string, any>;
  status: string;
  lastAttemptTime?: Date;
  metadata: Record<string, any>;
};

/**
 * Notification template type
 */
export type TNotificationTemplate = {
  templateId: string;
  name: string;
  description: string;
  content: string;
  variables: string[];
  channels: string[];
  version: string;
  metadata: Record<string, any>;
};

/**
 * Notification event type
 */
export type TNotificationEvent = {
  eventId: string;
  type: string;
  source: string;
  priority: TNotificationPriority;
  timestamp: Date;
  data: Record<string, any>;
  metadata: Record<string, any>;
};

/**
 * Notification result type
 */
export type TNotificationResult = {
  eventId: string;
  status: string;
  timestamp: Date;
  channels: TNotificationStatus[];
  metadata: Record<string, any>;
};

/**
 * Notification status type
 */
export type TNotificationStatus = {
  eventId: string;
  status: string;
  timestamp: Date;
  deliveryAttempts: number;
  lastAttemptTime?: Date;
  channels: Array<{
    channelId: string;
    status: string;
    lastAttemptTime?: Date;
  }>;
};

/**
 * Notification priority type
 */
export type TNotificationPriority = 'low' | 'medium' | 'high' | 'critical';

/**
 * Notification recipient type
 */
export type TNotificationRecipient = {
  recipientId: string;
  type: 'user' | 'group' | 'role';
  address: string;
  name?: string;
  metadata: Record<string, any>;
}; 