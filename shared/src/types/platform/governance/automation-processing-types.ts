/**
 * ============================================================================
 * OA FRAMEWORK - AUTOMATION PROCESSING SHARED TYPES
 * ============================================================================
 * 
 * **Document Type**: Shared Types Definition
 * **Version**: 2.1.1 - Type Consistency Update
 * **Created**: 2025-06-30 02:49:57 +03
 * **Updated**: 2025-07-01 - Type Consistency Fixes
 * **Authority**: President & CEO, E.Z. Consultancy
 * **Classification**: Core Infrastructure - Processing Framework Types
 * 
 * **File**: shared/src/types/platform/governance/automation-processing-types.ts
 * **Component**: G-SUB-05.2 - Processing Framework Shared Components
 * **Governance Authority**: docs/core/development-standards.md
 * **Compliance Status**: validated-by-m0
 * **Template Strategy**: on-demand-creation ✅ POLICY OVERRIDE
 * 
 * **Purpose**: 
 * Comprehensive shared types and interfaces for the Processing Framework subsystem
 * including transformation engines, event managers, notification systems, and
 * maintenance schedulers within the OA Framework governance system.
 * 
 * **Critical Requirements**:
 * - All shared types must use 'T' prefix (MANDATORY)
 * - All shared interfaces must use 'I' prefix (MANDATORY) 
 * - All shared constants must use UPPER_SNAKE_CASE (MANDATORY)
 * - Enterprise-grade implementation required
 * - Complete functionality - no simplification permitted
 * 
 * **Dependencies**:
 * - Core governance interfaces
 * - Platform performance metrics
 * - Security and compliance types
 * 
 * **Quality Standards**:
 * - TypeScript strict compliance
 * - Comprehensive error handling
 * - Production-ready implementation
 * - Full documentation coverage
 * 
 * ============================================================================
 */

// ✅ CRITICAL: All shared types must use 'T' prefix (MANDATORY)
// ✅ CRITICAL: All shared interfaces must use 'I' prefix (MANDATORY)
// ✅ CRITICAL: All shared constants must use UPPER_SNAKE_CASE (MANDATORY)

import {
  TValidationResult,
  TMetrics,
  TAuthorityData,
  TTimeRange
} from '../tracking/tracking-types';

import {
  TMemoryBoundary
} from './automation-engines/workflow-engines-types';

// =============================================================================
// CORE GOVERNANCE SERVICE INTERFACES
// =============================================================================

export interface IGovernanceService {
  id: string;
  authority: string;
  initialize(): Promise<void>;
  validate(): Promise<TValidationResult>;
  getMetrics(): Promise<TMetrics>;
  isReady(): boolean;
  shutdown(): Promise<void>;
}

// =============================================================================
// TRANSFORMATION ENGINE INTERFACES
// =============================================================================

/**
 * Transformation Engine Interface
 * Advanced data transformation and rule processing pipeline
 */
export interface ITransformationEngine extends IGovernanceService {
  /**
   * Transform data using specified schema
   */
  transformData(data: TTransformationData, schema: TTransformationSchema): Promise<TTransformationResult>;
  
  /**
   * Validate transformation configuration
   */
  validateTransformation(config: TTransformationConfig): Promise<TValidationResult>;
  
  /**
   * Orchestrate transformation pipeline execution
   */
  orchestrateTransformationPipeline(pipeline: TTransformationPipeline): Promise<TPipelineResult>;
  
  /**
   * Optimize transformation performance
   */
  optimizeTransformationPerformance(): Promise<void>;
  
  /**
   * Get transformation metrics
   */
  getTransformationMetrics(): Promise<TTransformationMetrics>;
  
  /**
   * Cache transformation results
   */
  cacheTransformationResults(key: string, result: TTransformationResult): Promise<void>;
}

/**
 * Transformation Service Interface
 * Service-level transformation operations
 */
export interface ITransformationService extends IGovernanceService {
  /**
   * Process transformation
   */
  processTransformation(data: TTransformationData): Promise<TTransformationResult>;
  
  /**
   * Validate schema
   */
  validateSchema(schema: TTransformationSchema): Promise<TValidationResult>;
  
  /**
   * Create pipeline
   */
  createPipeline(config: TTransformationConfig): Promise<TTransformationPipeline>;
}

// =============================================================================
// EVENT MANAGER INTERFACES
// =============================================================================

/**
 * Event Manager Interface
 * Comprehensive event-driven architecture and processing
 */
export interface IEventManager extends IGovernanceService {
  /**
   * Publish event to the system
   */
  publishEvent(event: TGovernanceEvent): Promise<TEventPublishResult>;
  
  /**
   * Subscribe to events with filtering
   */
  subscribeToEvents(subscription: TEventSubscription): Promise<TSubscriptionResult>;
  
  /**
   * Manage event streams
   */
  manageEventStreams(streams: TEventStream[]): Promise<TStreamManagementResult>;
  
  /**
   * Process event batch
   */
  processEventBatch(events: TGovernanceEvent[]): Promise<TBatchProcessingResult>;
  
  /**
   * Get event analytics
   */
  getEventAnalytics(): Promise<TEventAnalytics>;
  
  /**
   * Configure event routing
   */
  configureEventRouting(rules: TEventRoutingRule[]): Promise<void>;
}

/**
 * Event Service Interface
 * Service-level event operations
 */
export interface IEventService extends IGovernanceService {
  /**
   * Create event stream
   */
  createEventStream(config: TEventStreamConfig): Promise<TEventStream>;
  
  /**
   * Filter events
   */
  filterEvents(events: TGovernanceEvent[], filter: TEventFilter): Promise<TGovernanceEvent[]>;
  
  /**
   * Aggregate events
   */
  aggregateEvents(events: TGovernanceEvent[], aggregation: TEventAggregation): Promise<TAggregationResult>;
}

// =============================================================================
// NOTIFICATION SYSTEM INTERFACES
// =============================================================================

/**
 * Notification System Interface
 * Multi-channel notification delivery system
 */
export interface INotificationSystem extends IGovernanceService {
  /**
   * Send notification
   */
  sendNotification(notification: TNotification): Promise<TNotificationResult>;
  
  /**
   * Schedule notifications
   */
  scheduleNotifications(schedule: TNotificationSchedule): Promise<TScheduleResult>;
  
  /**
   * Track delivery status
   */
  trackDeliveryStatus(notificationId: string): Promise<TDeliveryStatus>;
  
  /**
   * Manage notification templates
   */
  manageNotificationTemplates(template: TNotificationTemplate): Promise<void>;
  
  /**
   * Configure notification channels
   */
  configureNotificationChannels(channels: TNotificationChannel[]): Promise<void>;
  
  /**
   * Get notification analytics
   */
  getNotificationAnalytics(): Promise<TNotificationAnalytics>;
}

/**
 * Messaging Service Interface
 * Service-level messaging operations
 */
export interface IMessagingService extends IGovernanceService {
  /**
   * Send message
   */
  sendMessage(message: TMessage, channel: TNotificationChannel): Promise<TMessageResult>;
  
  /**
   * Validate message format
   */
  validateMessageFormat(message: TMessage): Promise<TValidationResult>;
  
  /**
   * Generate message from template
   */
  generateMessageFromTemplate(template: TNotificationTemplate, data: Record<string, any>): Promise<TMessage>;
}

// =============================================================================
// MAINTENANCE SCHEDULER INTERFACES
// =============================================================================

/**
 * Maintenance Scheduler Interface
 * Advanced system maintenance scheduling and automation
 */
export interface IMaintenanceScheduler extends IGovernanceService {
  /**
   * Schedule maintenance task
   */
  scheduleMaintenance(maintenance: TMaintenanceTask): Promise<TMaintenanceScheduleResult>;
  
  /**
   * Monitor system health
   */
  monitorSystemHealth(): Promise<TSystemHealthStatus>;
  
  /**
   * Execute predictive maintenance
   */
  executePredictiveMaintenance(predictions: TMaintenancePrediction[]): Promise<TMaintenanceResult>;
  
  /**
   * Manage maintenance workflows
   */
  manageMaintenanceWorkflows(workflows: TMaintenanceWorkflow[]): Promise<void>;
  
  /**
   * Get maintenance analytics
   */
  getMaintenanceAnalytics(): Promise<TMaintenanceAnalytics>;
  
  /**
   * Optimize maintenance schedules
   */
  optimizeMaintenanceSchedules(): Promise<TScheduleOptimization>;
}

/**
 * Scheduling Service Interface
 * Service-level scheduling operations
 */
export interface ISchedulingService extends IGovernanceService {
  /**
   * Create schedule
   */
  createSchedule(config: TScheduleConfig): Promise<TMaintenanceSchedule>;
  
  /**
   * Validate schedule
   */
  validateSchedule(schedule: TMaintenanceSchedule): Promise<TValidationResult>;
  
  /**
   * Execute scheduled task
   */
  executeScheduledTask(task: TMaintenanceTask): Promise<TTaskExecutionResult>;
}

// =============================================================================
// CORE SHARED TYPES
// =============================================================================

export type TGovernanceService = {
  id: string;
  authority: string;
  processingLevel: TProcessingLevel;
  memoryBoundary: TMemoryBoundary;
  createdAt: Date;
  lastUpdated: Date;
};

export type TGovernanceRule = {
  ruleId: string;
  name: string;
  description: string;
  type: string;
  category: string;
  priority: string;
  status: string;
  conditions: Record<string, any>;
  actions: Record<string, any>;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  version: string;
};

export type TProcessingLevel = 'BASIC' | 'ADVANCED' | 'ENTERPRISE' | 'INTELLIGENT';
export type TProcessingStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'optimizing' | 'cancelled';

// =============================================================================
// TRANSFORMATION ENGINE TYPES
// =============================================================================

export type TTransformationEngineData = {
  transformations: TTransformation[];
  pipelines: TTransformationPipeline[];
  schemas: TTransformationSchema[];
  performanceMetrics: TTransformationMetrics;
  templates: TTransformationTemplate[];
  validationResults: TValidationResult[];
};

export type TTransformationData = {
  dataId: string;
  format: TTransformationFormat;
  content: Record<string, any>;
  metadata: TDataMetadata;
  size: number;
  checksum: string;
};

export type TTransformationSchema = {
  schemaId: string;
  name: string;
  version: string;
  sourceFormat: TTransformationFormat;
  targetFormat: TTransformationFormat;
  mappingRules: Record<string, any>;
  validationRules: Record<string, any>;
  metadata: Record<string, any>;
};

export type TTransformationResult = {
  resultId: string;
  transformationId: string;
  status: TProcessingStatus;
  transformedData: TTransformationData;
  executionTime: number;
  metrics: TTransformationMetrics;
  errors: TTransformationError[];
  warnings: TTransformationWarning[];
  metadata: Record<string, any>;
};

export type TTransformationConfig = {
  configId: string;
  pipelineId: string;
  parameters: Record<string, any>;
  optimization: TOptimizationConfig;
  validation: TValidationConfig;
  caching: TCachingConfig;
};

export type TTransformationPipeline = {
  pipelineId: string;
  name: string;
  description: string;
  stages: TTransformationStage[];
  dependencies: string[];
  configuration: TPipelineConfiguration;
  metadata: Record<string, any>;
};

export type TTransformationTemplate = {
  templateId: string;
  name: string;
  description: string;
  category: TTemplateCategory;
  transformationLogic: Record<string, any>;
  parameters: TTemplateParameter[];
  usageCount: number;
  rating: number;
};

export type TTransformationMetrics = {
  metricsId: string;
  timestamp: Date;
  throughput: number;
  latency: number;
  errorRate: number;
  successRate: number;
  resourceUtilization: TResourceUtilization;
  qualityMetrics: TQualityMetrics;
  metadata: Record<string, any>;
};

export type TPipelineResult = {
  pipelineId: string;
  executionId: string;
  status: TProcessingStatus;
  startTime: Date;
  endTime: Date;
  stageResults: TStageResult[];
  overallMetrics: TTransformationMetrics;
  metadata: Record<string, any>;
};

export type TTransformation = {
  transformationId: string;
  sourceData: TTransformationData;
  targetSchema: TTransformationSchema;
  status: TProcessingStatus;
  progress: number;
  startTime: Date;
  estimatedCompletion: Date;
};

// =============================================================================
// EVENT MANAGER TYPES
// =============================================================================

export type TEventManagerData = {
  events: TGovernanceEvent[];
  streams: TEventStream[];
  subscriptions: TEventSubscription[];
  publishResults: TEventPublishResult[];
  metrics: TEventMetrics;
  routingRules: TEventRoutingRule[];
};

export type TGovernanceEvent = {
  eventId: string;
  type: TEventType;
  source: string;
  timestamp: Date;
  data: Record<string, any>;
  priority: TEventPriority;
  tags: string[];
  correlationId?: string;
  metadata: Record<string, any>;
};

export type TEventSubscription = {
  subscriptionId: string;
  eventTypes: TEventType[];
  filter: TEventFilter;
  callback: TEventCallback;
  configuration: TSubscriptionConfig;
  metadata: Record<string, any>;
};

export type TEventStream = {
  streamId: string;
  name: string;
  description: string;
  configuration: TEventStreamConfig;
  status: TStreamStatus;
  metrics: TStreamMetrics;
  metadata: Record<string, any>;
};

export type TEventPublishResult = {
  eventId: string;
  status: TPublishStatus;
  publishTime: Date;
  deliveryCount: number;
  failedDeliveries: string[];
  metadata: Record<string, any>;
};

export type TSubscriptionResult = {
  subscriptionId: string;
  status: TSubscriptionStatus;
  eventCount: number;
  lastProcessed: Date;
  errors: TSubscriptionError[];
  metadata: Record<string, any>;
};

export type TStreamManagementResult = {
  managedStreams: string[];
  status: TManagementStatus;
  operations: TStreamOperation[];
  metrics: TStreamMetrics;
  metadata: Record<string, any>;
};

export type TBatchProcessingResult = {
  batchId: string;
  processedCount: number;
  failedCount: number;
  executionTime: number;
  results: TEventProcessingResult[];
  metadata: Record<string, any>;
};

export type TEventAnalytics = {
  totalEvents: number;
  eventsByType: Record<TEventType, number>;
  averageProcessingTime: number;
  errorRate: number;
  throughput: number;
  trends: TEventTrend[];
  metadata: Record<string, any>;
};

export type TEventRoutingRule = {
  ruleId: string;
  name: string;
  condition: TRoutingCondition;
  target: TRoutingTarget;
  priority: number;
  enabled: boolean;
  metadata: Record<string, any>;
};

// =============================================================================
// NOTIFICATION SYSTEM TYPES
// =============================================================================

export type TNotificationSystemData = {
  notifications: TNotification[];
  schedules: TNotificationSchedule[];
  deliveryResults: TNotificationResult[];
  templates: TNotificationTemplate[];
  channels: TNotificationChannel[];
  metrics: TNotificationMetrics;
};

export type TNotification = {
  notificationId: string;
  type: TNotificationType;
  recipient: TRecipient;
  content: TNotificationContent;
  priority: TNotificationPriority;
  channel: TNotificationChannel;
  scheduledTime?: Date;
  metadata: Record<string, any>;
};

export type TNotificationSchedule = {
  scheduleId: string;
  name: string;
  notifications: string[];
  schedule: TSchedulePattern;
  status: TScheduleStatus;
  configuration: TScheduleConfiguration;
  metadata: Record<string, any>;
};

export type TNotificationResult = {
  notificationId: string;
  status: TDeliveryStatus;
  sentTime: Date;
  deliveredTime?: Date;
  attempts: number;
  channel: TNotificationChannel;
  errors: TDeliveryError[];
  metadata: Record<string, any>;
};

export type TDeliveryStatus = {
  status: TDeliveryStatusType;
  message: string;
  timestamp: Date;
  retryCount: number;
  nextRetry?: Date;
  metadata: Record<string, any>;
};

export type TNotificationTemplate = {
  templateId: string;
  name: string;
  description: string;
  type: TNotificationType;
  content: TTemplateContent;
  variables: TTemplateVariable[];
  configuration: TTemplateConfiguration;
  metadata: Record<string, any>;
};

export type TNotificationChannel = {
  channelId: string;
  type: TChannelType;
  name: string;
  configuration: TChannelConfiguration;
  status: TChannelStatus;
  metrics: TChannelMetrics;
  metadata: Record<string, any>;
};

export type TNotificationAnalytics = {
  totalNotifications: number;
  deliveryRate: number;
  averageDeliveryTime: number;
  channelPerformance: Record<TChannelType, TChannelMetrics>;
  trends: TNotificationTrend[];
  metadata: Record<string, any>;
};

export type TMessage = {
  messageId: string;
  content: TMessageContent;
  format: TMessageFormat;
  encoding: string;
  size: number;
  metadata: Record<string, any>;
};

export type TMessageResult = {
  messageId: string;
  status: TMessageStatus;
  sentTime: Date;
  deliveryTime?: Date;
  metadata: Record<string, any>;
};

// =============================================================================
// MAINTENANCE SCHEDULER TYPES
// =============================================================================

export type TMaintenanceSchedulerData = {
  maintenanceTasks: TMaintenanceTask[];
  schedules: TMaintenanceSchedule[];
  healthMonitoring: TSystemHealthStatus[];
  predictions: TMaintenancePrediction[];
  executionResults: TMaintenanceResult[];
  metrics: TMaintenanceMetrics;
};

export type TMaintenanceTask = {
  taskId: string;
  name: string;
  description: string;
  type: TMaintenanceType;
  priority: TMaintenancePriority;
  schedule: TMaintenanceSchedule;
  dependencies: string[];
  estimatedDuration: number;
  resources: TMaintenanceResource[];
  metadata: Record<string, any>;
};

export type TMaintenanceSchedule = {
  scheduleId: string;
  name: string;
  pattern: TSchedulePattern;
  timezone: string;
  constraints: TScheduleConstraint[];
  status: TScheduleStatus;
  nextExecution: Date;
  metadata: Record<string, any>;
};

export type TSystemHealthStatus = {
  timestamp: Date;
  overallHealth: THealthScore;
  components: TComponentHealth[];
  metrics: TSystemMetrics;
  alerts: THealthAlert[];
  recommendations: TMaintenanceRecommendation[];
  metadata: Record<string, any>;
};

export type TMaintenancePrediction = {
  predictionId: string;
  component: string;
  predictedIssue: TIssueType;
  probability: number;
  timeToFailure: number;
  confidence: number;
  recommendedActions: TMaintenanceAction[];
  metadata: Record<string, any>;
};

export type TMaintenanceResult = {
  taskId: string;
  executionId: string;
  status: TExecutionStatus;
  startTime: Date;
  endTime: Date;
  actions: TMaintenanceAction[];
  metrics: TExecutionMetrics;
  issues: TMaintenanceIssue[];
  metadata: Record<string, any>;
};

export type TMaintenanceScheduleResult = {
  scheduleId: string;
  taskId: string;
  scheduledTime: Date;
  status: TSchedulingStatus;
  conflicts: TScheduleConflict[];
  metadata: Record<string, any>;
};

export type TMaintenanceAnalytics = {
  totalTasks: number;
  completionRate: number;
  averageExecutionTime: number;
  systemUptime: number;
  predictiveAccuracy: number;
  costSavings: number;
  trends: TMaintenanceTrend[];
  metadata: Record<string, any>;
};

export type TScheduleOptimization = {
  optimizationId: string;
  originalSchedule: TMaintenanceSchedule;
  optimizedSchedule: TMaintenanceSchedule;
  improvements: TOptimizationImprovement[];
  metrics: TOptimizationMetrics;
  metadata: Record<string, any>;
};

export type TMaintenanceWorkflow = {
  workflowId: string;
  name: string;
  steps: TWorkflowStep[];
  triggers: TWorkflowTrigger[];
  status: TWorkflowStatus;
  metadata: Record<string, any>;
};

export type TScheduleConfig = {
  configId: string;
  pattern: TSchedulePattern;
  constraints: TScheduleConstraint[];
  optimization: TScheduleOptimization;
  notifications: TNotificationConfig[];
  metadata: Record<string, any>;
};

export type TTaskExecutionResult = {
  taskId: string;
  executionId: string;
  status: TExecutionStatus;
  duration: number;
  output: Record<string, any>;
  errors: TExecutionError[];
  metadata: Record<string, any>;
};

// =============================================================================
// SHARED ENUMS AND UTILITY TYPES (✅ FIXED: Consistent with implementations)
// =============================================================================

// Transformation Types
export type TTransformationFormat = 'JSON' | 'XML' | 'CSV' | 'AVRO' | 'PARQUET' | 'YAML' | 'BINARY';
export type TTransformationStage = 'VALIDATION' | 'PREPROCESSING' | 'TRANSFORMATION' | 'POSTPROCESSING' | 'OPTIMIZATION';
export type TTemplateCategory = 'DATA_MAPPING' | 'VALIDATION' | 'FILTERING' | 'AGGREGATION' | 'CUSTOM';

// Event Types
export type TEventType = 'GOVERNANCE' | 'SECURITY' | 'PERFORMANCE' | 'ERROR' | 'AUDIT' | 'SYSTEM' | 'USER' | 'CUSTOM';
export type TEventPriority = 'low' | 'medium' | 'high' | 'critical' | 'emergency';
export type TStreamStatus = 'active' | 'paused' | 'stopped' | 'error' | 'maintenance';
export type TPublishStatus = 'published' | 'failed' | 'queued' | 'processing';
export type TSubscriptionStatus = 'active' | 'paused' | 'cancelled' | 'error';
export type TManagementStatus = 'success' | 'partial' | 'failed' | 'in_progress';

// ✅ FIXED: Notification Types - Aligned with implementations
export type TNotificationType = 'email' | 'sms' | 'push' | 'webhook' | 'slack' | 'teams' | 'custom';
export type TNotificationPriority = 'low' | 'medium' | 'high' | 'urgent';
export type TChannelType = 'email' | 'sms' | 'push' | 'webhook' | 'slack' | 'teams' | 'custom';
export type TChannelStatus = 'active' | 'inactive' | 'error' | 'maintenance';
export type TDeliveryStatusType = 'pending' | 'sent' | 'delivered' | 'failed' | 'retry' | 'cancelled' | 'partial';
// TScheduleStatus defined above for Rule Report Scheduler
export type TMessageFormat = 'text' | 'html' | 'markdown' | 'json' | 'xml';
export type TMessageStatus = 'sent' | 'delivered' | 'failed' | 'pending';

// ✅ FIXED: Maintenance Types - Aligned with implementations
export type TMaintenanceType = 'preventive' | 'predictive' | 'corrective' | 'emergency' | 'scheduled';
export type TMaintenancePriority = 'low' | 'medium' | 'high' | 'critical' | 'emergency';
export type TExecutionStatus = 'scheduled' | 'running' | 'completed' | 'failed' | 'cancelled' | 'skipped';
export type TSchedulingStatus = 'scheduled' | 'conflict' | 'failed' | 'cancelled';
export type THealthScore = 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
export type TIssueType = 'performance' | 'capacity' | 'security' | 'compliance' | 'hardware' | 'software';
export type TWorkflowStatus = 'active' | 'paused' | 'completed' | 'failed' | 'cancelled';

// ✅ PHASE 6: Rule Report Scheduler Enum Types
export type TReportType = 'compliance' | 'governance' | 'audit' | 'performance' | 'security' | 'custom';
export type TScheduleType = 'cron' | 'interval' | 'manual' | 'event-driven' | 'conditional';
export type TSchedulePriority = 'low' | 'normal' | 'high' | 'critical' | 'urgent';
export type TScheduleStatus = 'scheduled' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused';
export type TReportFormat = 'pdf' | 'csv' | 'json' | 'xml' | 'excel' | 'html' | 'markdown';
export type TDeliveryMethod = 'email' | 'ftp' | 'sftp' | 'api' | 's3' | 'local' | 'webhook';
export type TBatchExecutionMode = 'sequential' | 'parallel' | 'hybrid';
export type TOptimizationType = 'resource' | 'performance' | 'conflict_resolution' | 'load_balancing';

// =============================================================================
// ALERT MANAGER ENUM TYPES - PHASE 7
// =============================================================================

export type TAlertType = 'rule_violation' | 'performance' | 'security' | 'compliance' | 'system' | 'custom';
export type TAlertSeverity = 'info' | 'warning' | 'error' | 'critical' | 'fatal';
export type TAlertStatus = 'new' | 'acknowledged' | 'in_progress' | 'resolved' | 'closed' | 'suppressed';
export type TAlertPriority = 'low' | 'medium' | 'high' | 'urgent' | 'critical';
export type TAlertCategory = 'governance' | 'operational' | 'security' | 'performance' | 'compliance';
export type TAlertAction = 'acknowledge' | 'resolve' | 'close' | 'escalate' | 'suppress' | 'assign';
export type TAlertChannelType = 'email' | 'sms' | 'slack' | 'teams' | 'webhook' | 'dashboard' | 'pager';
export type TSuppressionType = 'time_based' | 'condition_based' | 'maintenance_window' | 'manual';
export type TCorrelationType = 'temporal' | 'causal' | 'similarity' | 'pattern_based';
export type TAlertGroupBy = 'type' | 'severity' | 'source' | 'status' | 'category' | 'time';
export type TAlertSortBy = 'timestamp' | 'severity' | 'priority' | 'status' | 'source';

// =============================================================================
// PROCESSING EVENT TYPES
// =============================================================================

export type TProcessingEvent = {
  eventId: string;
  type: 'start' | 'progress' | 'complete' | 'error' | 'warning';
  componentId: string;
  data: Record<string, any>;
  timestamp: Date;
};

export type TProcessingContext = {
  contextId: string;
  authority: TAuthorityData;
  memoryBoundary: 'enforced' | 'relaxed';
  processingLevel: TProcessingLevel;
  metadata: Record<string, any>;
};

// =============================================================================
// ERROR TYPES
// =============================================================================

export class TransformationProcessingError extends Error {
  constructor(
    message: string,
    public readonly transformationId: string,
    public readonly stage: TTransformationStage,
    public readonly data: TTransformationData
  ) {
    super(message);
    this.name = 'TransformationProcessingError';
  }
}

export class EventProcessingError extends Error {
  constructor(
    message: string,
    public readonly eventId: string,
    public readonly stream: TEventStream
  ) {
    super(message);
    this.name = 'EventProcessingError';
  }
}

export class NotificationDeliveryError extends Error {
  constructor(
    message: string,
    public readonly notificationId: string,
    public readonly channel: TNotificationChannel
  ) {
    super(message);
    this.name = 'NotificationDeliveryError';
  }
}

export class MaintenanceSchedulingError extends Error {
  constructor(
    message: string,
    public readonly maintenanceId: string,
    public readonly task: TMaintenanceTask
  ) {
    super(message);
    this.name = 'MaintenanceSchedulingError';
  }
}

// =============================================================================
// CONSTANTS
// =============================================================================

export const TRANSFORMATION_PROCESSING_TIMEOUT = 120000; // 2 minutes
export const MAX_TRANSFORMATION_PIPELINE_STAGES = 20;
export const EVENT_PROCESSING_BATCH_SIZE = 1000;
export const EVENT_STREAM_BUFFER_SIZE = 10000;
export const NOTIFICATION_DELIVERY_TIMEOUT = 30000;
export const MAX_NOTIFICATION_RETRY_COUNT = 5;
export const MAINTENANCE_SCHEDULE_OPTIMIZATION_INTERVAL = 86400000; // 24 hours
export const SYSTEM_HEALTH_CHECK_INTERVAL = 60000; // 1 minute
export const PREDICTIVE_MAINTENANCE_ANALYSIS_INTERVAL = 3600000; // 1 hour
export const PROCESSING_FRAMEWORK_METRICS_INTERVAL = 30000;

// =============================================================================
// UTILITY TYPES (PLACEHOLDERS FOR COMPLEX TYPES)
// =============================================================================

export type TDataMetadata = Record<string, any>;
export type TOptimizationConfig = Record<string, any>;
export type TValidationConfig = Record<string, any>;
export type TCachingConfig = Record<string, any>;
export type TPipelineConfiguration = Record<string, any>;
export type TTemplateParameter = Record<string, any>;
export type TResourceUtilization = Record<string, any>;
export type TQualityMetrics = Record<string, any>;
export type TStageResult = Record<string, any>;
export type TTransformationError = Record<string, any>;
export type TTransformationWarning = Record<string, any>;
export type TEventCallback = (...args: any[]) => Promise<void>;
export type TEventFilter = Record<string, any>;
export type TSubscriptionConfig = Record<string, any>;
export type TEventStreamConfig = Record<string, any>;
export type TStreamMetrics = Record<string, any>;
export type TSubscriptionError = Record<string, any>;
export type TStreamOperation = Record<string, any>;
export type TEventProcessingResult = Record<string, any>;
export type TEventTrend = Record<string, any>;
export type TRoutingCondition = Record<string, any>;
export type TRoutingTarget = Record<string, any>;
export type TEventMetrics = Record<string, any>;
export type TRecipient = Record<string, any>;
export type TNotificationContent = Record<string, any>;
export type TSchedulePattern = Record<string, any>;
export type TScheduleConfiguration = Record<string, any>;
export type TDeliveryError = Record<string, any>;
export type TTemplateContent = Record<string, any>;
export type TTemplateVariable = Record<string, any>;
export type TTemplateConfiguration = Record<string, any>;
export type TChannelConfiguration = Record<string, any>;
export type TChannelMetrics = Record<string, any>;
export type TNotificationTrend = Record<string, any>;
export type TMessageContent = Record<string, any>;
export type TMaintenanceResource = Record<string, any>;
export type TScheduleConstraint = Record<string, any>;
export type TComponentHealth = Record<string, any>;
export type TSystemMetrics = Record<string, any>;
export type THealthAlert = Record<string, any>;
export type TMaintenanceRecommendation = Record<string, any>;
export type TMaintenanceAction = Record<string, any>;
export type TExecutionMetrics = Record<string, any>;
export type TMaintenanceIssue = Record<string, any>;
export type TScheduleConflict = Record<string, any>;
export type TMaintenanceTrend = Record<string, any>;
export type TOptimizationImprovement = Record<string, any>;
export type TOptimizationMetrics = Record<string, any>;
export type TWorkflowStep = Record<string, any>;
export type TWorkflowTrigger = Record<string, any>;
export type TNotificationConfig = Record<string, any>;
export type TExecutionError = Record<string, any>;
export type TMaintenanceMetrics = Record<string, any>;
export type TScheduleResult = Record<string, any>;
export type TAggregationResult = Record<string, any>;
export type TEventAggregation = Record<string, any>;
export type TStepValidationResult = Record<string, any>;
export type TNotificationMetrics = Record<string, any>;

// =============================================================================
// PHASE 6: RULE REPORT SCHEDULER TYPES - MISSING DEFINITIONS
// =============================================================================

export type TRecurringScheduleResult = {
  scheduleId: string;
  reportId: string;
  status: TSchedulingStatus;
  nextExecution: Date;
  totalExecutions: number;
  conflicts: TScheduleConflict[];
  estimatedResource: TResourceEstimate;
  metadata: Record<string, any>;
};

export type TBatchScheduleResult = {
  batchId: string;
  status: TSchedulingStatus;
  reportCount: number;
  estimatedDuration: number;
  resourceRequirements: TResourceRequirement[];
  resourceAvailability: TResourceAvailability;
  executionPlan: TExecutionPlan;
  metadata: Record<string, any>;
};

export type TCancelResult = {
  scheduleId: string;
  cancelled: boolean;
  executionCancelled: boolean;
  resourcesReleased: boolean;
  timestamp: Date;
  metadata: Record<string, any>;
};

export type TModifyResult = {
  scheduleId: string;
  modified: boolean;
  conflicts: TScheduleConflict[];
  nextExecution: Date;
  resourceImpact: TResourceImpact;
  metadata: Record<string, any>;
};

export type TScheduleStatusResult = {
  scheduleId: string;
  schedule: TReportSchedule;
  status: TScheduleStatus;
  nextExecution?: Date;
  lastExecution?: Date;
  executionHistory: TExecutionHistoryEntry[];
  activeExecution?: TActiveExecutionInfo;
  statistics: TScheduleStatistics;
  resourceUsage: TResourceUsage;
  metadata: Record<string, any>;
};

export type TExecutionResult = {
  executionId: string;
  reportId: string;
  status: 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  duration?: number;
  outputPath?: string;
  errorMessage?: string;
  metrics: TExecutionMetrics;
  metadata: Record<string, any>;
};

export type TSchedulingAnalytics = {
  overview: TAnalyticsOverview;
  performance: TPerformanceAnalytics;
  scheduleDistribution: TScheduleDistribution;
  optimization: TOptimizationAnalytics;
  forecasting: TForecastingAnalytics;
};

// Supporting types for Rule Report Scheduler
// TScheduleConflict defined below

export type TResourceEstimate = {
  cpu: number;
  memory: number;
  storage: number;
  duration: number;
  cost?: number;
};

export type TResourceRequirement = {
  type: 'cpu' | 'memory' | 'storage' | 'network';
  amount: number;
  unit: string;
  duration: number;
  priority: TSchedulePriority;
};

export type TResourceAvailability = {
  available: boolean;
  reason?: string;
  alternativeSlots?: Date[];
  estimatedWaitTime?: number;
};

export type TExecutionPlan = {
  planId: string;
  steps: TExecutionStep[];
  totalDuration: number;
  parallelism: number;
  dependencies: TExecutionDependency[];
  resourceProfile: TResourceProfile;
};

export type TExecutionStep = {
  stepId: string;
  scheduleId: string;
  estimatedStartTime: Date;
  estimatedDuration: number;
  dependencies: string[];
  resources: TResourceRequirement[];
};

export type TResourceImpact = {
  cpuDelta: number;
  memoryDelta: number;
  storageDelta: number;
  durationDelta: number;
  costDelta?: number;
};

export type TExecutionHistoryEntry = {
  executionId: string;
  executionTime: Date;
  duration: number;
  status: 'completed' | 'failed' | 'cancelled';
  outputSize?: number;
  errorMessage?: string;
};

export type TActiveExecutionInfo = {
  executionId: string;
  startTime: Date;
  progress: number;
  estimatedCompletion: Date;
};

export type TScheduleStatistics = {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageDuration: number;
  successRate: number;
  lastExecutionTime?: Date;
};

export type TResourceUsage = {
  current: TResourceProfile;
  average: TResourceProfile;
  peak: TResourceProfile;
  trend: 'increasing' | 'decreasing' | 'stable';
};

export type TResourceProfile = {
  cpu: number;
  memory: number;
  storage: number;
  network: number;
};

// TExecutionMetrics defined below

export type TAnalyticsOverview = {
  totalSchedules: number;
  activeSchedules: number;
  completedExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  successRate: number;
  lastUpdated: Date;
};

export type TPerformanceAnalytics = {
  throughput: number;
  resourceUtilization: TResourceProfile;
  performanceScore: number;
  reliabilityScore: number;
  trends: TPerformanceTrend[];
};

export type TScheduleDistribution = {
  byType: Record<TReportType, number>;
  byPriority: Record<TSchedulePriority, number>;
  byStatus: Record<TScheduleStatus, number>;
  byTimeOfDay: Record<string, number>;
};

export type TOptimizationAnalytics = {
  optimizationScore: number;
  conflictRate: number;
  resourceEfficiency: number;
  recommendations: TOptimizationRecommendation[];
};

export type TForecastingAnalytics = {
  predictedLoad: TLoadForecast[];
  resourceDemand: TResourceDemandForecast[];
  capacityRecommendations: TCapacityRecommendation[];
};

export type TPerformanceTrend = {
  metric: string;
  trend: 'up' | 'down' | 'stable';
  change: number;
  period: string;
};

export type TOptimizationRecommendation = {
  type: 'schedule' | 'resource' | 'configuration';
  priority: TSchedulePriority;
  title: string;
  description: string;
  expectedBenefit: string;
  effort: 'low' | 'medium' | 'high';
};

export type TLoadForecast = {
  timeSlot: Date;
  predictedLoad: number;
  confidence: number;
  factors: string[];
};

export type TResourceDemandForecast = {
  timeSlot: Date;
  resourceType: string;
  predictedDemand: number;
  confidence: number;
};

export type TCapacityRecommendation = {
  resourceType: string;
  currentCapacity: number;
  recommendedCapacity: number;
  justification: string;
  timeline: string;
};

export type TRecurrencePattern = {
  type: 'cron' | 'interval' | 'manual';
  expression?: string;
  interval?: number;
  intervalUnit?: 'minutes' | 'hours' | 'days' | 'weeks' | 'months';
  timezone: string;
};

export type TCompressionType = 'none' | 'gzip' | 'zip' | 'brotli';

export type TEncryptionConfig = {
  enabled: boolean;
  algorithm?: string;
  keyId?: string;
};

export type TRetentionPolicy = {
  days: number;
  action: 'delete' | 'archive' | 'compress';
};

// TRecipient already defined above

export type TEmailConfig = {
  subject: string;
  template?: string;
  attachments?: boolean;
};

export type TFTPConfig = {
  host: string;
  port: number;
  username: string;
  path: string;
  passive: boolean;
};

export type TAPIConfig = {
  endpoint: string;
  method: 'POST' | 'PUT' | 'PATCH';
  headers: Record<string, string>;
  authentication?: TAuthConfig;
};

export type TS3Config = {
  bucket: string;
  region: string;
  keyPrefix: string;
  storageClass: string;
};

export type TAuthConfig = {
  type: 'bearer' | 'basic' | 'apikey';
  token?: string;
  credentials?: { username: string; password: string };
  apiKey?: string;
};

export type TIntervalUnit = 'seconds' | 'minutes' | 'hours' | 'days' | 'weeks' | 'months';
export type TDayOfWeek = 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';
export type TBatchScheduleStatus = 'scheduled' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused';

export type TFailureHandling = {
  continueOnFailure: boolean;
  maxFailures: number;
  notifyOnFailure: boolean;
};

export type TConsolidationConfig = {
  format: 'zip' | 'tar' | 'folder';
  includeIndex: boolean;
  summaryReport: boolean;
};

export type TScheduleWarning = {
  code: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
  field?: string;
};

export type TResourceSavings = {
  cpu: number;
  memory: number;
  storage: number;
  cost?: number;
};

export type TPerformanceImprovements = {
  throughputIncrease: number;
  latencyReduction: number;
  reliabilityIncrease: number;
};

export type TEstimatedBenefits = {
  monthlySavings: number;
  performanceGain: number;
  reliabilityImprovement: number;
  maintenanceReduction: number;
};

export type TExecutionDependency = {
  dependencyId: string;
  type: 'schedule' | 'resource' | 'data';
  target: string;
  condition: string;
};

export type TResourceReservation = {
  reservationId: string;
  resources: Record<string, number>;
  expiresAt: Date;
};

export type TScheduleModifications = Partial<Pick<TReportSchedule, 
  'name' | 'description' | 'priority' | 'schedule' | 'outputConfig' | 'deliveryConfig' | 'timeout' | 'tags'
>>;

// =============================================================================
// ALERT MANAGER COMPLEX TYPES - PHASE 7
// =============================================================================

export type TAlertCondition = {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'regex';
  value: any;
  caseSensitive?: boolean;
};

export type TTimeRestriction = {
  startTime: string;
  endTime: string;
  daysOfWeek: TDayOfWeek[];
  timezone: string;
  excludeHolidays: boolean;
};

export type TAlertAcknowledgment = {
  acknowledgedBy: string;
  acknowledgedAt: Date;
  comment?: string;
  estimatedResolutionTime?: Date;
};

export type TAlertResolution = {
  resolvedBy: string;
  resolvedAt: Date;
  resolutionType: 'fixed' | 'false_positive' | 'duplicate' | 'maintenance';
  comment: string;
  rootCause?: string;
  preventionMeasures?: string[];
};

export type TEscalationStep = {
  stepNumber: number;
  channels: string[];
  timeout: number;
  condition?: TAlertCondition;
  skipIfAcknowledged: boolean;
};

export type TTemplateFormatting = {
  format: 'text' | 'html' | 'markdown' | 'json';
  styling: Record<string, any>;
  includeMetadata: boolean;
};

export type TTemplateLocalization = {
  defaultLanguage: string;
  supportedLanguages: string[];
  translations: Record<string, Record<string, string>>;
};

export type TRateLimitConfig = {
  enabled: boolean;
  maxAlertsPerMinute: number;
  maxAlertsPerHour: number;
  burstAllowance: number;
  cooldownPeriod: number;
};

export type TAlertChannelConfiguration = {
  endpoint?: string;
  apiKey?: string;
  credentials?: Record<string, string>;
  headers?: Record<string, string>;
  timeout: number;
  retries: number;
  customSettings: Record<string, any>;
};

export type TAlertTrend = {
  period: string;
  alertCount: number;
  averageSeverity: number;
  escalationRate: number;
  resolutionTime: number;
  topSources: string[];
};

export type TAlertSourceMetrics = {
  source: string;
  alertCount: number;
  averageSeverity: TAlertSeverity;
  escalationRate: number;
  resolutionTime: number;
};

export type TAlertSummary = {
  totalAlerts: number;
  newAlerts: number;
  acknowledgedAlerts: number;
  resolvedAlerts: number;
  escalatedAlerts: number;
  suppressedAlerts: number;
  averageResolutionTime: number;
  topSeverities: Record<TAlertSeverity, number>;
  topSources: string[];
};

export type TAlertRecommendation = {
  type: 'routing' | 'suppression' | 'escalation' | 'template' | 'channel';
  priority: TAlertPriority;
  title: string;
  description: string;
  expectedBenefit: string;
  implementationEffort: 'low' | 'medium' | 'high';
  metadata: Record<string, any>;
};

export type TAlertGlobalSettings = {
  defaultSeverity: TAlertSeverity;
  defaultPriority: TAlertPriority;
  maxEscalationLevels: number;
  defaultSuppressionDuration: number;
  enableCorrelation: boolean;
  correlationWindow: number;
  enableDeduplication: boolean;
  deduplicationWindow: number;
  retentionPeriod: number;
  archiveAfterDays: number;
};

export type TImmediateReport = {
  reportId: string;
  reportType: TReportType;
  format?: TReportFormat;
  priority: TSchedulePriority;
  parameters?: Record<string, any>;
  outputPath?: string;
  deliveryConfig?: TDeliveryConfiguration;
};

export type TActiveExecution = {
  executionId: string;
  scheduleId: string;
  reportId: string;
  startTime: Date;
  status: 'running' | 'paused' | 'completed' | 'failed';
  progress: number;
  estimatedCompletion: Date;
};

export type TExecutedReport = {
  executionId: string;
  scheduleId: string;
  reportId: string;
  executionTime: Date;
  duration: number;
  status: 'completed' | 'failed';
  outputPath?: string;
  errorMessage?: string;
  metrics: TExecutionMetrics;
};

export type TExecutionQueueItem = {
  queueId: string;
  scheduleId: string;
  reportId: string;
  scheduledTime: Date;
  priority: TSchedulePriority;
  dependencies: string[];
  status: 'queued' | 'processing' | 'completed' | 'failed';
};

export type TRetryPolicy = {
  maxRetries: number;
  retryDelay: number;
  backoffMultiplier: number;
  maxRetryDelay: number;
};

export type TNotificationRecord = {
  recordId: string;
  notificationId: string;
  timestamp: Date;
  recipient: string;
  status: 'sent' | 'delivered' | 'failed';
  channel: TChannelType;
  metadata: Record<string, any>;
};

export type TSchedulerPerformanceData = {
  averageExecutionTime: number;
  throughput: number;
  resourceUtilization: TResourceUtilization;
  errorRate: number;
  successRate: number;
  queueLength: number;
  activeExecutions: number;
};

// Rule Report Scheduler specific types
export type TReportSchedule = {
  scheduleId: string;
  reportId: string;
  name: string;
  description: string;
  reportType: TReportType;
  schedule: TScheduleDefinition;
  outputConfig: TOutputConfiguration;
  deliveryConfig: TDeliveryConfiguration;
  priority: TSchedulePriority;
  constraints: TScheduleConstraint[];
  dependencies: string[];
  retryPolicy: TRetryPolicy;
  timeout: number;
  timezone: string;
  tags: string[];
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  status: TScheduleStatus;
};

export type TRecurringReportSchedule = TReportSchedule & {
  recurrencePattern: TRecurrencePattern;
  endDate?: Date;
  maxExecutions?: number;
  executionCount: number;
  nextExecution: Date;
  lastExecution?: Date;
  skipHolidays: boolean;
  businessDaysOnly: boolean;
};

export type TBatchReportSchedule = {
  batchId: string;
  name: string;
  description: string;
  reports: TReportSchedule[];
  batchSchedule: TScheduleDefinition;
  executionMode: TBatchExecutionMode;
  parallelExecution: boolean;
  maxConcurrency: number;
  failureHandling: TFailureHandling;
  consolidatedOutput: boolean;
  consolidationConfig?: TConsolidationConfig;
  priority: TSchedulePriority;
  createdAt: Date;
  updatedAt: Date;
  status: TBatchScheduleStatus;
};

export type TScheduleReportResult = {
  scheduleId: string;
  reportId: string;
  status: TSchedulingStatus;
  scheduledTime: Date;
  estimatedDuration: number;
  resourceReservation: TResourceReservation;
  conflicts: TScheduleConflict[];
  warnings: TScheduleWarning[];
  metadata: Record<string, any>;
};

export type TSchedulingMetrics = {
  totalSchedules: number;
  activeSchedules: number;
  completedExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  schedulesByType: Record<TReportType, number>;
  schedulesByPriority: Record<TSchedulePriority, number>;
  resourceUtilization: TResourceUtilization;
  performanceScore: number;
  reliabilityScore: number;
  lastUpdated: Date;
};

export type TOptimizationResult = {
  optimizationId: string;
  timestamp: Date;
  optimizationType: TOptimizationType;
  schedulesOptimized: number;
  resourceSavings: TResourceSavings;
  performanceImprovements: TPerformanceImprovements;
  recommendations: TOptimizationRecommendation[];
  conflictsResolved: number;
  estimatedBenefits: TEstimatedBenefits;
};

export type TScheduleDefinition = {
  type: TScheduleType;
  cronExpression?: string;
  interval?: number;
  intervalUnit?: TIntervalUnit;
  executionTime?: string;
  executionDays?: TDayOfWeek[];
  startDate?: Date;
  endDate?: Date;
  timezone: string;
  businessHoursOnly?: boolean;
  excludeHolidays?: boolean;
};

export type TOutputConfiguration = {
  format: TReportFormat;
  compression: TCompressionType;
  encryption: TEncryptionConfig;
  watermark: boolean;
  templateId?: string;
  customFields: Record<string, any>;
  fileNamingPattern: string;
  outputDirectory: string;
  retention: TRetentionPolicy;
};

export type TDeliveryConfiguration = {
  method: TDeliveryMethod;
  recipients: TRecipient[];
  emailConfig?: TEmailConfig;
  ftpConfig?: TFTPConfig;
  apiConfig?: TAPIConfig;
  s3Config?: TS3Config;
  notificationConfig: TNotificationConfig;
  deliveryRetries: number;
  deliveryTimeout: number;
};

// =============================================================================
// ALERT MANAGER DATA TYPES - PHASE 7
// =============================================================================

export type TAlertManagerData = {
  alerts: TGovernanceAlert[];
  channels: TAlertChannel[];
  routingRules: TAlertRoutingRule[];
  suppressionRules: TAlertSuppressionRule[];
  templates: TAlertTemplate[];
  escalationPolicies: TEscalationPolicy[];
  deliveryResults: TAlertDeliveryResult[];
  analytics: TAlertAnalytics;
};

export type TGovernanceAlert = {
  alertId: string;
  type: TAlertType;
  severity: TAlertSeverity;
  source: string;
  title: string;
  description: string;
  ruleId?: string;
  triggeredBy: string;
  timestamp: Date;
  status: TAlertStatus;
  priority: TAlertPriority;
  category: TAlertCategory;
  tags: string[];
  metadata: Record<string, any>;
  acknowledgment?: TAlertAcknowledgment;
  resolution?: TAlertResolution;
  escalationLevel: number;
  suppressionId?: string;
  correlationId?: string;
  affectedComponents: string[];
  recommendedActions: string[];
};

export type TAlertChannel = {
  channelId: string;
  type: TAlertChannelType;
  name: string;
  description: string;
  configuration: TAlertChannelConfiguration;
  status: TChannelStatus;
  deliveryMethods: TDeliveryMethod[];
  retryPolicy: TRetryPolicy;
  rateLimiting: TRateLimitConfig;
  enabled: boolean;
  metadata: Record<string, any>;
};

export type TAlertRoutingRule = {
  ruleId: string;
  name: string;
  description: string;
  conditions: TAlertCondition[];
  channels: string[];
  escalationPolicy: string;
  priority: number;
  enabled: boolean;
  timeRestrictions: TTimeRestriction[];
  metadata: Record<string, any>;
};

export type TAlertSuppressionRule = {
  suppressionId: string;
  name: string;
  description: string;
  conditions: TAlertCondition[];
  suppressionType: TSuppressionType;
  duration: number;
  startTime?: Date;
  endTime?: Date;
  enabled: boolean;
  metadata: Record<string, any>;
};

export type TAlertTemplate = {
  templateId: string;
  name: string;
  description: string;
  alertType: TAlertType;
  titleTemplate: string;
  bodyTemplate: string;
  variables: TTemplateVariable[];
  formatting: TTemplateFormatting;
  localization: TTemplateLocalization;
  metadata: Record<string, any>;
};

export type TEscalationPolicy = {
  policyId: string;
  name: string;
  description: string;
  steps: TEscalationStep[];
  maxEscalationLevel: number;
  timeoutBetweenSteps: number;
  fallbackChannel: string;
  enabled: boolean;
  metadata: Record<string, any>;
};

export type TAlertGenerationResult = {
  alertId: string;
  status: 'generated' | 'failed' | 'suppressed';
  timestamp: Date;
  channels: string[];
  suppressionReason?: string;
  correlatedAlerts: string[];
  metadata: Record<string, any>;
};

export type TAlertDeliveryResult = {
  alertId: string;
  deliveryId: string;
  channel: TAlertChannel;
  status: TDeliveryStatusType;
  sentTime: Date;
  deliveredTime?: Date;
  attempts: number;
  errors: TDeliveryError[];
  responseTime: number;
  metadata: Record<string, any>;
};

export type TAlertLifecycleResult = {
  alertId: string;
  action: TAlertAction;
  status: 'success' | 'failed' | 'partial';
  timestamp: Date;
  performedBy: string;
  previousState: TAlertStatus;
  newState: TAlertStatus;
  metadata: Record<string, any>;
};

export type TSuppressionResult = {
  suppressionId: string;
  affectedAlerts: string[];
  suppressionType: TSuppressionType;
  duration: number;
  status: 'active' | 'expired' | 'cancelled';
  metadata: Record<string, any>;
};

export type TAlertAnalytics = {
  totalAlerts: number;
  alertsByType: Record<TAlertType, number>;
  alertsBySeverity: Record<TAlertSeverity, number>;
  averageResponseTime: number;
  averageResolutionTime: number;
  escalationRate: number;
  suppressionRate: number;
  channelPerformance: Record<TAlertChannelType, TChannelMetrics>;
  trends: TAlertTrend[];
  topAlertSources: TAlertSourceMetrics[];
  metadata: Record<string, any>;
};

export type TEscalationResult = {
  alertId: string;
  escalationLevel: number;
  escalatedTo: string[];
  escalationTime: Date;
  status: 'escalated' | 'failed' | 'max_level_reached';
  nextEscalationTime?: Date;
  metadata: Record<string, any>;
};

export type TCorrelationResult = {
  correlationId: string;
  primaryAlert: string;
  correlatedAlerts: string[];
  correlationType: TCorrelationType;
  confidence: number;
  timestamp: Date;
  metadata: Record<string, any>;
};

export type TAlertReport = {
  reportId: string;
  criteria: TAlertReportCriteria;
  generatedTime: Date;
  alerts: TGovernanceAlert[];
  summary: TAlertSummary;
  trends: TAlertTrend[];
  recommendations: TAlertRecommendation[];
  metadata: Record<string, any>;
};

export type TAlertChannelConfig = {
  type: TAlertChannelType;
  name: string;
  configuration: TAlertChannelConfiguration;
  deliveryMethods: TDeliveryMethod[];
  retryPolicy: TRetryPolicy;
  rateLimiting: TRateLimitConfig;
  metadata: Record<string, any>;
};

export type TAlertConfiguration = {
  alertTypes: TAlertType[];
  severityLevels: TAlertSeverity[];
  channels: TAlertChannelConfig[];
  routingRules: TAlertRoutingRule[];
  escalationPolicies: TEscalationPolicy[];
  suppressionRules: TAlertSuppressionRule[];
  templates: TAlertTemplate[];
  globalSettings: TAlertGlobalSettings;
};

export type TAlertReportCriteria = {
  timeRange: TTimeRange;
  alertTypes?: TAlertType[];
  severityLevels?: TAlertSeverity[];
  sources?: string[];
  status?: TAlertStatus[];
  tags?: string[];
  includeResolved: boolean;
  groupBy: TAlertGroupBy[];
  sortBy: TAlertSortBy;
  limit?: number;
};

// =============================================================================
// ALERT MANAGER INTERFACES - PHASE 7
// =============================================================================

/**
 * Alert Manager Interface
 * Comprehensive alert generation, management, and delivery system
 */
export interface IAlertManager extends IGovernanceService {
  /**
   * Generate alert based on rule violation or system event
   */
  generateAlert(alert: TGovernanceAlert): Promise<TAlertGenerationResult>;
  
  /**
   * Send alert through configured channels
   */
  sendAlert(alertId: string, channels: TAlertChannel[]): Promise<TAlertDeliveryResult>;
  
  /**
   * Manage alert lifecycle (acknowledge, resolve, close)
   */
  manageAlertLifecycle(alertId: string, action: TAlertAction): Promise<TAlertLifecycleResult>;
  
  /**
   * Configure alert routing and escalation policies
   */
  configureAlertRouting(rules: TAlertRoutingRule[]): Promise<void>;
  
  /**
   * Suppress alerts based on rules and conditions
   */
  suppressAlerts(suppressionRules: TAlertSuppressionRule[]): Promise<TSuppressionResult>;
  
  /**
   * Get alert analytics and metrics
   */
  getAlertAnalytics(): Promise<TAlertAnalytics>;
  
  /**
   * Manage alert templates and customization
   */
  manageAlertTemplates(template: TAlertTemplate): Promise<void>;
  
  /**
   * Process alert escalation
   */
  processAlertEscalation(alertId: string): Promise<TEscalationResult>;
}

/**
 * Alerting Service Interface
 * Service-level alert operations and management
 */
export interface IAlertingService extends IGovernanceService {
  /**
   * Create alert channel
   */
  createAlertChannel(config: TAlertChannelConfig): Promise<TAlertChannel>;
  
  /**
   * Validate alert configuration
   */
  validateAlertConfiguration(config: TAlertConfiguration): Promise<TValidationResult>;
  
  /**
   * Process alert correlation and deduplication
   */
  processAlertCorrelation(alerts: TGovernanceAlert[]): Promise<TCorrelationResult>;
  
  /**
   * Generate alert reports
   */
  generateAlertReports(criteria: TAlertReportCriteria): Promise<TAlertReport>;
}