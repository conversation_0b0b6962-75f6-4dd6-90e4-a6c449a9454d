/**
 * @file Environment Manager Types
 * @filepath shared/src/types/platform/governance/management-configuration/environment-manager-types.ts
 * @reference G-TSK-07.SUB-07.1.IMP-04
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-07-05
 * @modified 2025-07-05
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-007-management-administration-architecture
 * @governance-dcr DCR-foundation-006-security-integration-standards
 * @governance-status approved
 * @governance-compliance authority-validated
 */

import {
  TGovernanceService
} from '../automation-processing-types';

import {
  TAuthorityData
} from '../../tracking/tracking-types';

// ============================================================================
// ENVIRONMENT MANAGER TYPES
// ============================================================================

/**
 * Environment Manager Data Type
 * Core data structure for environment management operations
 */
export type TEnvironmentManagerData = TGovernanceService & {
  /** Environment registry */
  environments: Map<string, TEnvironmentData>;
  
  /** Environment templates */
  templates: Map<string, TEnvironmentTemplate>;
  
  /** Deployment history */
  deployments: Map<string, TDeploymentHistory>;
  
  /** Performance metrics */
  performanceMetrics: TEnvironmentPerformanceMetrics;
  
  /** Resource pools */
  resourcePools: Map<string, TResourcePool>;
  
  /** Security policies */
  securityPolicies: Map<string, TSecurityPolicy>;
  
  /** Monitoring configurations */
  monitoringConfigs: Map<string, TMonitoringConfig>;
  
  /** Authority data */
  authorityData: TAuthorityData;
};

/**
 * Environment Data Type
 * Complete environment configuration and state
 */
export type TEnvironmentData = {
  /** Environment identifier */
  id: string;
  
  /** Environment name */
  name: string;
  
  /** Environment description */
  description?: string;
  
  /** Environment type */
  type: 'development' | 'testing' | 'staging' | 'production' | 'sandbox' | 'integration' | 'performance';
  
  /** Environment status */
  status: 'creating' | 'active' | 'inactive' | 'deploying' | 'failed' | 'maintenance' | 'archived';
  
  /** Environment version */
  version: string;
  
  /** Environment configuration */
  configuration: TEnvironmentConfiguration;
  
  /** Environment state */
  state: TEnvironmentState;
  
  /** Environment variables */
  variables: Record<string, string>;
  
  /** Environment secrets */
  secrets: Record<string, string>;
  
  /** Environment tags */
  tags: Record<string, string>;
  
  /** Environment metadata */
  metadata: TEnvironmentMetadata;
};

/**
 * Environment Configuration Type
 * Technical configuration for environment setup
 */
export type TEnvironmentConfiguration = {
  /** Resource configuration */
  resources: TResourceConfiguration;
  
  /** Network configuration */
  network: TNetworkConfiguration;
  
  /** Storage configuration */
  storage: TStorageConfiguration;
  
  /** Security configuration */
  security: TSecurityConfiguration;
  
  /** Monitoring configuration */
  monitoring: TMonitoringConfiguration;
  
  /** Backup configuration */
  backup: TBackupConfiguration;
  
  /** Scaling configuration */
  scaling: TScalingConfiguration;
  
  /** Dependencies configuration */
  dependencies: TDependencyConfiguration[];
};

/**
 * Environment State Type
 * Runtime state and status information
 */
export type TEnvironmentState = {
  /** Current status */
  status: 'initializing' | 'ready' | 'running' | 'stopped' | 'failed' | 'maintenance';
  
  /** Health status */
  health: 'healthy' | 'unhealthy' | 'degraded' | 'unknown';
  
  /** Uptime in seconds */
  uptime: number;
  
  /** Last health check */
  lastHealthCheck: Date;
  
  /** Instance information */
  instances: {
    total: number;
    running: number;
    failed: number;
    pending: number;
  };
  
  /** Resource utilization */
  resourceUtilization: {
    cpu: number;
    memory: number;
    storage: number;
    network: number;
  };
  
  /** Active connections */
  activeConnections: number;
  
  /** Error count */
  errorCount: number;
  
  /** Warning count */
  warningCount: number;
};

/**
 * Environment Metadata Type
 * Administrative and tracking information
 */
export type TEnvironmentMetadata = {
  /** Creation timestamp */
  createdAt: Date;
  
  /** Last modified timestamp */
  modifiedAt: Date;
  
  /** Created by user */
  createdBy: string;
  
  /** Last modified by user */
  modifiedBy: string;
  
  /** Environment owner */
  owner: string;
  
  /** Environment team */
  team?: string;
  
  /** Environment project */
  project?: string;
  
  /** Environment cost center */
  costCenter?: string;
  
  /** Environment region */
  region?: string;
  
  /** Environment zone */
  zone?: string;
  
  /** Authority data */
  authority: TAuthorityData;
  
  /** Compliance status */
  compliance: {
    status: 'compliant' | 'non-compliant' | 'unknown';
    lastCheck: Date;
    standards: string[];
    violations: string[];
  };
  
  /** Audit information */
  audit: {
    lastAudit: Date;
    auditStatus: 'passed' | 'failed' | 'pending';
    findings: string[];
    recommendations: string[];
  };
};

/**
 * Resource Configuration Type
 * Hardware and infrastructure resource allocation
 */
export type TResourceConfiguration = {
  /** CPU configuration */
  cpu: {
    limit: number;
    request: number;
    architecture: 'x86_64' | 'arm64' | 'mixed';
    cores: number;
    threads: number;
  };
  
  /** Memory configuration */
  memory: {
    limit: number;
    request: number;
    type: 'standard' | 'high-memory' | 'optimized';
    swapEnabled: boolean;
  };
  
  /** Storage configuration */
  storage: {
    limit: number;
    type: 'ssd' | 'hdd' | 'nvme' | 'network';
    class: 'standard' | 'premium' | 'archive';
    iops: number;
    throughput: number;
  };
  
  /** Network configuration */
  network: {
    bandwidth: number;
    type: 'standard' | 'premium' | 'dedicated';
    latency: number;
    packetLoss: number;
  };
  
  /** GPU configuration (optional) */
  gpu?: {
    count: number;
    type: string;
    memory: number;
    compute: string;
  };
};

/**
 * Network Configuration Type
 * Network infrastructure and connectivity setup
 */
export type TNetworkConfiguration = {
  /** VPC configuration */
  vpc: {
    id: string;
    cidr: string;
    region: string;
    dns: {
      servers: string[];
      searchDomains: string[];
      resolver: string;
    };
  };
  
  /** Subnet configuration */
  subnets: {
    public: string[];
    private: string[];
    database: string[];
  };
  
  /** Security groups */
  securityGroups: {
    ingress: TSecurityRule[];
    egress: TSecurityRule[];
  };
  
  /** Load balancer configuration */
  loadBalancer?: {
    type: 'application' | 'network' | 'gateway';
    scheme: 'internet-facing' | 'internal';
    healthCheck: THealthCheckConfig;
    sslPolicy: string;
    certificates: string[];
  };
  
  /** CDN configuration */
  cdn?: {
    provider: string;
    distribution: string;
    origins: string[];
    caching: TCachingConfig;
    security: TCDNSecurityConfig;
  };
};

/**
 * Security Rule Type
 * Network security rule configuration
 */
export type TSecurityRule = {
  /** Rule type */
  type: 'allow' | 'deny';
  
  /** Protocol */
  protocol: 'tcp' | 'udp' | 'icmp' | 'all';
  
  /** Port range */
  ports: {
    from: number;
    to: number;
  };
  
  /** Source CIDR or security group */
  source: string;
  
  /** Destination CIDR or security group */
  destination?: string;
  
  /** Rule description */
  description?: string;
  
  /** Rule priority */
  priority: number;
  
  /** Rule enabled */
  enabled: boolean;
};

/**
 * Storage Configuration Type
 * Storage and persistence configuration
 */
export type TStorageConfiguration = {
  /** Primary storage */
  primary: {
    type: 'block' | 'object' | 'file';
    size: number;
    class: 'standard' | 'premium' | 'archive';
    encryption: boolean;
    backup: boolean;
    replication?: TReplicationConfig;
  };
  
  /** Additional storage volumes */
  volumes: {
    name: string;
    type: 'block' | 'object' | 'file';
    size: number;
    mountPath: string;
    class: 'standard' | 'premium' | 'archive';
    encryption: boolean;
    backup: boolean;
  }[];
  
  /** Database storage */
  database?: {
    type: 'relational' | 'nosql' | 'cache' | 'search';
    engine: string;
    version: string;
    size: number;
    class: 'standard' | 'premium' | 'memory-optimized';
    backup: TBackupConfig;
    replication?: TReplicationConfig;
  };
};

/**
 * Security Configuration Type
 * Security settings and policies
 */
export type TSecurityConfiguration = {
  /** Authentication configuration */
  authentication: {
    type: 'basic' | 'oauth' | 'saml' | 'ldap' | 'custom';
    provider?: string;
    config: Record<string, any>;
    mfa: boolean;
    sessionTimeout: number;
  };
  
  /** Authorization configuration */
  authorization: {
    type: 'rbac' | 'abac' | 'custom';
    policies: Record<string, any>;
    defaultRole: string;
    adminRoles: string[];
  };
  
  /** Encryption configuration */
  encryption: {
    atRest: {
      enabled: boolean;
      algorithm: string;
      keyManagement: 'managed' | 'customer' | 'hybrid';
      keyRotation: boolean;
      keyRotationInterval: number;
    };
    inTransit: {
      enabled: boolean;
      tlsVersion: string;
      cipherSuites: string[];
      certificates: TCertificateConfig[];
    };
  };
  
  /** Compliance configuration */
  compliance: {
    standards: string[];
    policies: Record<string, any>;
    audit: {
      enabled: boolean;
      retention: number;
      destinations: string[];
    };
  };
  
  /** Vulnerability scanning */
  vulnerabilityScanning: {
    enabled: boolean;
    schedule: string;
    tools: string[];
    severity: 'low' | 'medium' | 'high' | 'critical';
  };
};

/**
 * Monitoring Configuration Type
 * Monitoring and observability setup
 */
export type TMonitoringConfiguration = {
  /** Metrics configuration */
  metrics: {
    enabled: boolean;
    interval: number;
    retention: number;
    exporters: string[];
    customMetrics: TCustomMetric[];
  };
  
  /** Logging configuration */
  logging: {
    enabled: boolean;
    level: 'debug' | 'info' | 'warn' | 'error';
    retention: number;
    destinations: string[];
    format: 'json' | 'text' | 'structured';
    sampling: number;
  };
  
  /** Tracing configuration */
  tracing: {
    enabled: boolean;
    samplingRate: number;
    exporters: string[];
    customTags: Record<string, string>;
  };
  
  /** Alerting configuration */
  alerting: {
    enabled: boolean;
    rules: TAlertRule[];
    destinations: string[];
    escalation: TEscalationPolicy[];
  };
  
  /** Health checks */
  healthChecks: {
    enabled: boolean;
    endpoints: string[];
    interval: number;
    timeout: number;
    retries: number;
  };
};

/**
 * Backup Configuration Type
 * Backup and recovery setup
 */
export type TBackupConfiguration = {
  /** Backup enabled */
  enabled: boolean;
  
  /** Backup schedule */
  schedule: string;
  
  /** Backup retention (days) */
  retention: number;
  
  /** Backup destinations */
  destinations: string[];
  
  /** Backup type */
  type: 'full' | 'incremental' | 'differential';
  
  /** Backup encryption */
  encryption: {
    enabled: boolean;
    algorithm: string;
    key: string;
  };
  
  /** Backup compression */
  compression: {
    enabled: boolean;
    algorithm: string;
    level: number;
  };
  
  /** Backup verification */
  verification: {
    enabled: boolean;
    schedule: string;
    checksum: boolean;
  };
};

/**
 * Scaling Configuration Type
 * Auto-scaling configuration
 */
export type TScalingConfiguration = {
  /** Scaling enabled */
  enabled: boolean;
  
  /** Scaling type */
  type: 'horizontal' | 'vertical' | 'both';
  
  /** Minimum instances */
  minInstances: number;
  
  /** Maximum instances */
  maxInstances: number;
  
  /** Target metrics */
  targetMetrics: {
    cpu: number;
    memory: number;
    requests: number;
    responseTime: number;
  };
  
  /** Scale up policy */
  scaleUpPolicy: {
    threshold: number;
    cooldown: number;
    increment: number;
    evaluationPeriods: number;
  };
  
  /** Scale down policy */
  scaleDownPolicy: {
    threshold: number;
    cooldown: number;
    decrement: number;
    evaluationPeriods: number;
  };
};

/**
 * Dependency Configuration Type
 * Environment dependency setup
 */
export type TDependencyConfiguration = {
  /** Dependency name */
  name: string;
  
  /** Dependency type */
  type: 'service' | 'database' | 'cache' | 'queue' | 'external' | 'library';
  
  /** Dependency version */
  version: string;
  
  /** Dependency configuration */
  configuration: Record<string, any>;
  
  /** Dependency health check */
  healthCheck: {
    enabled: boolean;
    url: string;
    interval: number;
    timeout: number;
    retries: number;
    expectedStatus: number;
  };
  
  /** Dependency criticality */
  criticality: 'critical' | 'high' | 'medium' | 'low';
  
  /** Dependency fallback */
  fallback?: {
    enabled: boolean;
    strategy: 'circuit-breaker' | 'retry' | 'cache' | 'default';
    configuration: Record<string, any>;
  };
};

/**
 * Environment Performance Metrics Type
 * Performance tracking and analytics
 */
export type TEnvironmentPerformanceMetrics = {
  /** Total environments */
  totalEnvironments: number;
  
  /** Active environments */
  activeEnvironments: number;
  
  /** Failed environments */
  failedEnvironments: number;
  
  /** Average deployment time */
  averageDeploymentTime: number;
  
  /** Average startup time */
  averageStartupTime: number;
  
  /** Average response time */
  averageResponseTime: number;
  
  /** Total deployments */
  totalDeployments: number;
  
  /** Successful deployments */
  successfulDeployments: number;
  
  /** Failed deployments */
  failedDeployments: number;
  
  /** Resource utilization */
  resourceUtilization: {
    cpu: number;
    memory: number;
    storage: number;
    network: number;
  };
  
  /** Cost metrics */
  costMetrics: {
    totalCost: number;
    costPerEnvironment: number;
    costPerHour: number;
    costTrend: 'increasing' | 'decreasing' | 'stable';
  };
  
  /** Availability metrics */
  availabilityMetrics: {
    uptime: number;
    downtime: number;
    availability: number;
    mttr: number; // Mean Time To Recovery
    mtbf: number; // Mean Time Between Failures
  };
};

// ============================================================================
// SUPPORTING TYPES
// ============================================================================

/**
 * Environment Template Type
 * Reusable environment configuration templates
 */
export type TEnvironmentTemplate = {
  /** Template identifier */
  id: string;
  
  /** Template name */
  name: string;
  
  /** Template description */
  description?: string;
  
  /** Template version */
  version: string;
  
  /** Template configuration */
  configuration: TEnvironmentConfiguration;
  
  /** Template variables */
  variables: Record<string, any>;
  
  /** Template metadata */
  metadata: {
    createdAt: Date;
    modifiedAt: Date;
    createdBy: string;
    modifiedBy: string;
    usage: number;
    tags: Record<string, string>;
  };
};

/**
 * Deployment History Type
 * Deployment tracking and history
 */
export type TDeploymentHistory = {
  /** Deployment identifier */
  deploymentId: string;
  
  /** Environment identifier */
  environmentId: string;
  
  /** Deployment status */
  status: 'success' | 'failure' | 'in-progress' | 'cancelled' | 'rollback';
  
  /** Deployment strategy */
  strategy: 'blue-green' | 'rolling' | 'canary' | 'recreate';
  
  /** Deployment start time */
  startTime: Date;
  
  /** Deployment end time */
  endTime?: Date;
  
  /** Deployment duration */
  duration?: number;
  
  /** Deployment version */
  version: string;
  
  /** Deployment logs */
  logs: string[];
  
  /** Deployment errors */
  errors: string[];
  
  /** Deployment artifacts */
  artifacts: string[];
  
  /** Deployment metrics */
  metrics: {
    resourcesDeployed: number;
    testsExecuted: number;
    testsPassed: number;
    testsFailed: number;
    coveragePercentage: number;
  };
};

/**
 * Resource Pool Type
 * Resource pooling and allocation
 */
export type TResourcePool = {
  /** Pool identifier */
  id: string;
  
  /** Pool name */
  name: string;
  
  /** Pool type */
  type: 'compute' | 'storage' | 'network' | 'mixed';
  
  /** Pool capacity */
  capacity: {
    total: number;
    available: number;
    allocated: number;
    reserved: number;
  };
  
  /** Pool resources */
  resources: {
    cpu: number;
    memory: number;
    storage: number;
    network: number;
  };
  
  /** Pool allocation */
  allocations: {
    environmentId: string;
    allocated: number;
    timestamp: Date;
  }[];
};

/**
 * Security Policy Type
 * Security policy configuration
 */
export type TSecurityPolicy = {
  /** Policy identifier */
  id: string;
  
  /** Policy name */
  name: string;
  
  /** Policy description */
  description?: string;
  
  /** Policy type */
  type: 'access' | 'data' | 'network' | 'compliance';
  
  /** Policy rules */
  rules: {
    condition: string;
    action: 'allow' | 'deny' | 'log' | 'alert';
    priority: number;
    enabled: boolean;
  }[];
  
  /** Policy enforcement */
  enforcement: 'strict' | 'lenient' | 'monitor';
  
  /** Policy metadata */
  metadata: {
    createdAt: Date;
    modifiedAt: Date;
    createdBy: string;
    modifiedBy: string;
    version: string;
  };
};

/**
 * Monitoring Config Type
 * Monitoring configuration
 */
export type TMonitoringConfig = {
  /** Configuration identifier */
  id: string;
  
  /** Configuration name */
  name: string;
  
  /** Monitoring targets */
  targets: string[];
  
  /** Monitoring metrics */
  metrics: string[];
  
  /** Monitoring interval */
  interval: number;
  
  /** Monitoring retention */
  retention: number;
  
  /** Monitoring alerts */
  alerts: TAlertRule[];
  
  /** Monitoring dashboards */
  dashboards: string[];
};

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Health Check Config Type
 */
export type THealthCheckConfig = {
  path: string;
  interval: number;
  timeout: number;
  healthyThreshold: number;
  unhealthyThreshold: number;
  matcher: string;
};

/**
 * Caching Config Type
 */
export type TCachingConfig = {
  enabled: boolean;
  ttl: number;
  policies: Record<string, any>;
  headers: Record<string, string>;
};

/**
 * CDN Security Config Type
 */
export type TCDNSecurityConfig = {
  waf: boolean;
  ddos: boolean;
  geoBlocking: string[];
  rateLimiting: {
    enabled: boolean;
    requests: number;
    window: number;
  };
};

/**
 * Replication Config Type
 */
export type TReplicationConfig = {
  type: 'sync' | 'async';
  regions: string[];
  replicas: number;
  consistency: 'strong' | 'eventual';
};

/**
 * Backup Config Type
 */
export type TBackupConfig = {
  enabled: boolean;
  schedule: string;
  retention: number;
  encryption: boolean;
  compression: boolean;
};

/**
 * Certificate Config Type
 */
export type TCertificateConfig = {
  type: 'self-signed' | 'ca-signed' | 'acm';
  domains: string[];
  algorithm: string;
  keySize: number;
  validityPeriod: number;
};

/**
 * Custom Metric Type
 */
export type TCustomMetric = {
  name: string;
  description: string;
  type: 'counter' | 'gauge' | 'histogram' | 'summary';
  labels: Record<string, string>;
  unit: string;
};

/**
 * Alert Rule Type
 */
export type TAlertRule = {
  name: string;
  description?: string;
  condition: string;
  severity: 'info' | 'warning' | 'critical';
  threshold: number;
  duration: number;
  evaluationInterval: number;
  labels: Record<string, string>;
  annotations: Record<string, string>;
  enabled: boolean;
};

/**
 * Escalation Policy Type
 */
export type TEscalationPolicy = {
  name: string;
  rules: {
    delay: number;
    targets: string[];
    repeat: number;
  }[];
  fallback: string[];
}; 