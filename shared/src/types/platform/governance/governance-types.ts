/**
 * @file Governance Types
 * @description Type definitions for governance system components
 */

import { TTimeRange } from '../tracking/tracking-types';

/**
 * Rule execution metrics type
 */
export type TRuleExecutionMetrics = {
  ruleId: string;
  executionTime: number;
  status: 'success' | 'failure' | 'timeout';
  resourceUsage: {
    cpu: number;
    memory: number;
    network: number;
  };
  timestamp: Date;
  metadata: Record<string, any>;
};

/**
 * Compliance metrics type
 */
export type TComplianceMetrics = {
  ruleId: string;
  complianceScore: number;
  violations: number;
  timestamp: Date;
  metadata: Record<string, any>;
};

/**
 * Rule performance metrics type
 */
export type TRulePerformanceMetrics = {
  ruleId: string;
  timeRange: TTimeRange;
  executionMetrics: {
    averageExecutionTime: number;
    totalExecutions: number;
    executionTimeData: any[];
  };
  resourceMetrics: {
    averageMemoryUsage: number;
    averageCpuUsage: number;
    memoryUsageData: any[];
    cpuUsageData: any[];
  };
  performanceScore: number;
  recommendations: string[];
};

/**
 * System metrics type
 */
export type TSystemMetrics = {
  timeRange: TTimeRange;
  metrics: {
    ruleExecutions: any;
    ruleResults: any;
    complianceScores: any;
    complianceViolations: any;
    memoryUsage: any;
    cpuUsage: any;
    systemHealth: {
      totalDataPointsCollected: number;
      totalAlertsGenerated: number;
      avgCollectionTimeMs: number;
      errorCount: number;
      activeAlerts: number;
    };
  };
  systemScore: number;
  recommendations: string[];
  summary: {
    totalRuleExecutions: number;
    averageExecutionTime: number;
    systemHealthScore: number;
    activeAlertsCount: number;
  };
};

/**
 * Metrics dashboard type
 */
export type TMetricsDashboard = {
  dashboardId: string;
  title: string;
  description: string;
  timeRange: TTimeRange;
  panels: Array<{
    panelId: string;
    title: string;
    type: 'chart' | 'gauge' | 'table' | 'stat';
    metrics: any[];
    configuration: Record<string, any>;
  }>;
  metadata: Record<string, any>;
};

/**
 * Export format type
 */
export type TExportFormat = 'json' | 'csv' | 'excel' | 'pdf';

/**
 * Export result type
 */
export type TExportResult = {
  exportId: string;
  format: TExportFormat;
  url: string;
  expiresAt: Date;
  metadata: Record<string, any>;
}; 