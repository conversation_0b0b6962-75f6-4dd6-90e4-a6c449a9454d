/**
 * @file Shared Logging Mixin for Infrastructure Services
 * @component logging-mixin
 * @authority-level infrastructure
 * @governance-adr ADR-security-002-logging-consistency
 */

export interface ILoggingService {
  logInfo(message: string, details?: Record<string, unknown>): void;
  logWarning(message: string, details?: Record<string, unknown>): void;
  logError(message: string, error: unknown, details?: Record<string, unknown>): void;
  logDebug(message: string, details?: Record<string, unknown>): void;
}

/**
 * Mixin function to add consistent logging to any class
 */
export function withLogging<T extends new (...args: any[]) => {}>(
  Base: T,
  serviceName: string
) {
  // Helper function to format error messages consistently
  const formatErrorMessage = (error: unknown): string => {
    if (error instanceof Error) {
      // For Error objects, use only the message (not the full toString which includes "Error: ")
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    if (error === null) {
      return 'null';
    }
    if (error === undefined) {
      return 'undefined';
    }
    // For other types, convert to string
    return String(error);
  };

  return class extends Base implements ILoggingService {
    public logInfo(message: string, details?: Record<string, unknown>): void {
      console.log(`[INFO] ${serviceName}: ${message}`, details || '');
    }

    public logWarning(message: string, details?: Record<string, unknown>): void {
      console.warn(`[WARNING] ${serviceName}: ${message}`, details || '');
    }

    public logError(message: string, error: unknown, details?: Record<string, unknown>): void {
      // CRITICAL FIX: Consistent error message formatting
      const errorMessage = formatErrorMessage(error);
      console.error(`[ERROR] ${serviceName}: ${message} - ${errorMessage}`, details || '');
    }

    public logDebug(message: string, details?: Record<string, unknown>): void {
      if (process.env.NODE_ENV === 'development' || process.env.DEBUG) {
        console.debug(`[DEBUG] ${serviceName}: ${message}`, details || '');
      }
    }
  };
}

/**
 * Simple logging implementation for infrastructure services
 */
export class SimpleLogger implements ILoggingService {
  constructor(private serviceName: string) {}

  logInfo(message: string, details?: Record<string, unknown>): void {
    console.log(`[INFO] ${this.serviceName}: ${message}`, details || '');
  }

  logWarning(message: string, details?: Record<string, unknown>): void {
    console.warn(`[WARNING] ${this.serviceName}: ${message}`, details || '');
  }

  logError(message: string, error: unknown, details?: Record<string, unknown>): void {
    // CRITICAL FIX: Consistent error message formatting
    const errorMessage = this._formatErrorMessage(error);
    console.error(`[ERROR] ${this.serviceName}: ${message} - ${errorMessage}`, details || '');
  }

  logDebug(message: string, details?: Record<string, unknown>): void {
    if (process.env.NODE_ENV === 'development' || process.env.DEBUG === 'true') {
      console.debug(`[DEBUG] ${this.serviceName}: ${message}`, details || '');
    }
  }

  /**
   * Format error messages consistently
   * CRITICAL FIX: Ensures test expectations match actual output
   */
  private _formatErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      // For Error objects, use only the message (not the full toString which includes "Error: ")
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    if (error === null) {
      return 'null';
    }
    if (error === undefined) {
      return 'undefined';
    }
    // For other types, convert to string
    return String(error);
  }
}