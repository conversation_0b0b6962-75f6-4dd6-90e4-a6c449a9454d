/**
 * @file AtomicCircularBuffer Integration Test Suite
 * @component atomic-circular-buffer-integration-tests
 * @authority-level critical-memory-safety-testing
 * @governance-adr ADR-security-002-integration-testing
 */

// Configure Jest timeout for integration tests
jest.setTimeout(15000); // 15 second timeout

// Mock timer functions to prevent real timer creation
const mockSetInterval = jest.fn((_callback: Function, ms: number) => {
  console.log(`[MOCK] setInterval called with ${ms}ms - MOCKED, NO REAL INTERVAL CREATED`);
  return 'mock-interval-id';
});

const mockClearInterval = jest.fn((id: any) => {
  console.log(`[MOCK] clearInterval called for ${id} - MOCKED`);
});

const mockSetTimeout = jest.fn((_callback: Function, ms: number) => {
  console.log(`[MOCK] setTimeout called with ${ms}ms - MOCKED, NO REAL TIMEOUT CREATED`);
  return 'mock-timeout-id';
});

const mockClearTimeout = jest.fn((id: any) => {
  console.log(`[MOCK] clearTimeout called for ${id} - MOCKED`);
});

// Apply global mocks
(global as any).setInterval = mockSetInterval;
(global as any).clearInterval = mockClearInterval;
(global as any).setTimeout = mockSetTimeout;
(global as any).clearTimeout = mockClearTimeout;

// CRITICAL: Mock MemorySafeResourceManager to eliminate ALL resource allocation (Lesson 04)
jest.mock('../MemorySafeResourceManager', () => {
  const mockCalls = {
    createSafeInterval: jest.fn((_callback?: any, _intervalMs?: number, _name?: string) => 'mock-interval-id'),
    createSafeTimeout: jest.fn((_callback?: any, _timeoutMs?: number, _name?: string) => 'mock-timeout-id'),
    clearSafeInterval: jest.fn((_id: string) => {}),
    clearSafeTimeout: jest.fn((_id: string) => {})
  };

  class MockMemorySafeResourceManager {
    protected _isInitialized = false;
    protected _isShuttingDown = false;
    protected _limits: any;

    constructor(limits?: any) {
      this._limits = limits || {};
      console.log('[MOCK] MemorySafeResourceManager constructor called - NO RESOURCES CREATED');
    }

    protected async doInitialize(): Promise<void> {
      console.log('[MOCK] doInitialize called - MOCKED, NO REAL INITIALIZATION');
      this._isInitialized = true;
    }

    protected async doShutdown(): Promise<void> {
      console.log('[MOCK] doShutdown called - MOCKED, NO REAL SHUTDOWN');
      this._isShuttingDown = true;
    }

    public async initialize(): Promise<void> {
      console.log('[MOCK] initialize called - MOCKED');
      await this.doInitialize();
    }

    public async shutdown(): Promise<void> {
      console.log('[MOCK] shutdown called - MOCKED');
      await this.doShutdown();
    }

    public isHealthy(): boolean {
      console.log(`[MOCK] isHealthy called - shutting down: ${this._isShuttingDown}, returning: ${!this._isShuttingDown}`);
      return !this._isShuttingDown;
    }

    public getResourceMetrics(): any {
      return {
        totalResources: 0,
        intervals: 0,
        timeouts: 0,
        cacheSize: 0,
        connections: 0
      };
    }

    protected createSafeInterval(callback?: any, intervalMs?: number, name?: string): string {
      return mockCalls.createSafeInterval(callback, intervalMs, name);
    }

    protected createSafeTimeout(callback?: any, timeoutMs?: number, name?: string): string {
      return mockCalls.createSafeTimeout(callback, timeoutMs, name);
    }

    protected clearSafeInterval(id: string): void {
      mockCalls.clearSafeInterval(id);
    }

    protected clearSafeTimeout(id: string): void {
      mockCalls.clearSafeTimeout(id);
    }

    public static forceGlobalCleanup(): void {
      console.log('[MOCK] MemorySafeResourceManager.forceGlobalCleanup() called - MOCKED');
    }

    public static getMockCalls() {
      return mockCalls;
    }
  }

  return {
    MemorySafeResourceManager: MockMemorySafeResourceManager,
    __mockCalls: mockCalls
  };
});

// Mock LoggingMixin
jest.mock('../LoggingMixin', () => {
  return {
    SimpleLogger: class MockSimpleLogger {
      private _name: string;

      constructor(name: string) {
        this._name = name;
      }

      logInfo(message: string, details?: Record<string, unknown>) {
        console.log(`[INFO] ${this._name}: ${message}`, details || '');
      }

      logWarning(message: string, details?: Record<string, unknown>) {
        console.warn(`[WARNING] ${this._name}: ${message}`, details || '');
      }

      logError(message: string, error: unknown, details?: Record<string, unknown>) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`[ERROR] ${this._name}: ${message} - ${errorMessage}`, details || '');
      }

      logDebug(message: string, details?: Record<string, unknown>) {
        if (process.env.NODE_ENV === 'development') {
          console.debug(`[DEBUG] ${this._name}: ${message}`, details || '');
        }
      }
    },
    ILoggingService: {}
  };
});

console.log('[TEST] AtomicCircularBuffer.integration.test.ts starting - relying on global mocks');

// Import AFTER global mocks are established
import { AtomicCircularBuffer } from '../AtomicCircularBuffer';

describe('AtomicCircularBuffer - Integration', () => {
  let buffer: AtomicCircularBuffer<string>;
  const maxSize = 10;

  beforeEach(async () => {
    console.log('[TEST] Creating new AtomicCircularBuffer for integration test');
    buffer = new AtomicCircularBuffer<string>(maxSize);
    await buffer.initialize();
    console.log('[TEST] AtomicCircularBuffer initialized successfully');
  });

  afterEach(async () => {
    console.log('[TEST] Cleaning up AtomicCircularBuffer');
    if (buffer) {
      try {
        await buffer.shutdown();
        console.log('[TEST] AtomicCircularBuffer shutdown completed');
      } catch (error) {
        console.warn('[TEST] Error during buffer shutdown:', error);
      }
    }
  });

  describe('MemorySafeResourceManager Integration', () => {
    it('should integrate properly with MemorySafeResourceManager', async () => {
      // Buffer should be initialized and healthy
      expect(buffer.isHealthy()).toBe(true);

      // Should track resource metrics
      const metrics = buffer.getResourceMetrics();
      expect(metrics).toBeDefined();
      expect(typeof metrics.totalResources).toBe('number');
      expect(metrics.totalResources).toBeGreaterThanOrEqual(0);

      // Should handle resource limits
      expect(buffer.getSize()).toBeLessThanOrEqual(maxSize);
    });

    it('should handle resource lifecycle properly', async () => {
      // Create multiple buffers to test resource management
      const buffers: AtomicCircularBuffer<string>[] = [];
      
      try {
        for (let i = 0; i < 3; i++) {
          const testBuffer = new AtomicCircularBuffer<string>(5);
          await testBuffer.initialize();
          buffers.push(testBuffer);
          
          // Add some data to each buffer
          await testBuffer.addItem(`buffer${i}_key1`, `buffer${i}_value1`);
          await testBuffer.addItem(`buffer${i}_key2`, `buffer${i}_value2`);
          
          expect(testBuffer.isHealthy()).toBe(true);
        }

        // All buffers should be working independently
        for (let i = 0; i < buffers.length; i++) {
          expect(buffers[i].getSize()).toBe(2);
          expect(buffers[i].getItem(`buffer${i}_key1`)).toBe(`buffer${i}_value1`);
        }

      } finally {
        // Clean up all buffers
        for (const testBuffer of buffers) {
          await testBuffer.shutdown();
          expect(testBuffer.isHealthy()).toBe(false);
        }
      }
    });

    it('should handle shared resource scenarios', async () => {
      // Test multiple buffers sharing the same resource manager patterns
      const buffer1 = new AtomicCircularBuffer<string>(5);
      const buffer2 = new AtomicCircularBuffer<string>(5);

      try {
        await buffer1.initialize();
        await buffer2.initialize();

        // Both should be healthy
        expect(buffer1.isHealthy()).toBe(true);
        expect(buffer2.isHealthy()).toBe(true);

        // Add data to both
        await buffer1.addItem('shared_test1', 'value1');
        await buffer2.addItem('shared_test2', 'value2');

        // Should maintain independence
        expect(buffer1.getItem('shared_test1')).toBe('value1');
        expect(buffer1.getItem('shared_test2')).toBeUndefined();
        expect(buffer2.getItem('shared_test2')).toBe('value2');
        expect(buffer2.getItem('shared_test1')).toBeUndefined();

      } finally {
        await buffer1.shutdown();
        await buffer2.shutdown();
      }
    });
  });

  describe('Logging Integration', () => {
    it('should implement ILoggingService interface correctly', () => {
      // Should have logging methods
      expect(typeof buffer.logInfo).toBe('function');
      expect(typeof buffer.logWarning).toBe('function');
      expect(typeof buffer.logError).toBe('function');
      expect(typeof buffer.logDebug).toBe('function');
    });

    it('should integrate logging with buffer operations', async () => {
      // Mock console to capture logs
      const originalConsole = console.log;
      const logMessages: string[] = [];
      console.log = jest.fn((message: string) => {
        logMessages.push(message);
      });

      try {
        // Perform operations that might trigger logging
        await buffer.addItem('log_test1', 'log_value1');
        buffer.logInfo('Test info message');
        
        // Should not interfere with normal operations
        expect(buffer.getItem('log_test1')).toBe('log_value1');
        expect(buffer.getSize()).toBe(1);

      } finally {
        console.log = originalConsole;
      }
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle operations on uninitialized buffer', async () => {
      const uninitializedBuffer = new AtomicCircularBuffer<string>(5);

      // Operations should either work or throw controlled errors
      try {
        await uninitializedBuffer.addItem('uninit_key', 'uninit_value');
        // If it doesn't throw, it should work correctly
        expect(uninitializedBuffer.getItem('uninit_key')).toBe('uninit_value');
      } catch (error) {
        // If it throws, it should be a controlled error
        expect(error).toBeInstanceOf(Error);
      }

      // Clean up
      try {
        await uninitializedBuffer.shutdown();
      } catch (error) {
        // Shutdown might also throw for uninitialized buffer
      }
    });

    it('should handle invalid input gracefully', async () => {
      // Test null and undefined keys
      await expect(buffer.addItem(null as any, 'null_key_value')).resolves.not.toThrow();
      await expect(buffer.addItem(undefined as any, 'undefined_key_value')).resolves.not.toThrow();

      // Test null and undefined values
      await expect(buffer.addItem('null_value_key', null as any)).resolves.not.toThrow();
      await expect(buffer.addItem('undefined_value_key', undefined as any)).resolves.not.toThrow();

      // Buffer should still be functional
      expect(buffer.isHealthy()).toBe(true);
    });

    it('should recover from temporary errors', async () => {
      // Add some items successfully
      await buffer.addItem('recovery1', 'value1');
      await buffer.addItem('recovery2', 'value2');

      expect(buffer.getSize()).toBe(2);

      // Simulate error condition and recovery
      try {
        // Force an error scenario (if possible)
        await buffer.addItem('', ''); // Empty key/value
      } catch (error) {
        // Should handle gracefully
      }

      // Buffer should still be functional after error
      expect(buffer.isHealthy()).toBe(true);
      await buffer.addItem('recovery3', 'value3');
      expect(buffer.getItem('recovery3')).toBe('value3');
    });
  });

  describe('End-to-End Workflow Scenarios', () => {
    it('should handle complete lifecycle workflow', async () => {
      // Phase 1: Initial setup and population
      const initialData = [
        { key: 'user1', value: 'John Doe' },
        { key: 'user2', value: 'Jane Smith' },
        { key: 'user3', value: 'Bob Johnson' }
      ];

      for (const item of initialData) {
        await buffer.addItem(item.key, item.value);
      }

      expect(buffer.getSize()).toBe(3);

      // Phase 2: Updates and modifications
      await buffer.addItem('user1', 'John Doe Updated');
      await buffer.addItem('user4', 'Alice Brown');

      expect(buffer.getSize()).toBe(4);
      expect(buffer.getItem('user1')).toBe('John Doe Updated');

      // Phase 3: Partial cleanup
      await buffer.removeItem('user2');
      expect(buffer.getSize()).toBe(3);
      expect(buffer.getItem('user2')).toBeUndefined();

      // Phase 4: Capacity testing
      for (let i = 5; i <= 15; i++) {
        await buffer.addItem(`user${i}`, `User ${i}`);
      }

      expect(buffer.getSize()).toBe(maxSize);

      // Phase 5: Final verification
      const allItems = buffer.getAllItems();
      expect(allItems.size).toBe(maxSize);
      expect(buffer.isHealthy()).toBe(true);

      const metrics = buffer.getMetrics();
      expect(metrics.totalOperations).toBeGreaterThan(0);
      expect(metrics.addOperations).toBeGreaterThan(0);
    });

    it('should handle concurrent workflow scenarios', async () => {
      // Simulate multiple concurrent workflows
      const workflows: Promise<void>[] = [];

      // Workflow 1: User management
      workflows.push((async () => {
        for (let i = 0; i < 5; i++) {
          await buffer.addItem(`workflow1_user${i}`, `User ${i}`);
          if (i > 2) {
            await buffer.removeItem(`workflow1_user${i - 2}`);
          }
        }
      })());

      // Workflow 2: Session management
      workflows.push((async () => {
        for (let i = 0; i < 5; i++) {
          await buffer.addItem(`workflow2_session${i}`, `Session ${i}`);
          buffer.getItem(`workflow2_session${Math.max(0, i - 1)}`);
        }
      })());

      // Workflow 3: Cache management
      workflows.push((async () => {
        for (let i = 0; i < 5; i++) {
          await buffer.addItem(`workflow3_cache${i}`, `Cache ${i}`);
        }
      })());

      // Execute all workflows concurrently
      await Promise.all(workflows);

      // Verify final state
      expect(buffer.getSize()).toBeLessThanOrEqual(maxSize);
      expect(buffer.isHealthy()).toBe(true);

      const metrics = buffer.getMetrics();
      expect(metrics.totalOperations).toBeGreaterThan(10);
    });

    it('should handle stress workflow with recovery', async () => {
      // Phase 1: Heavy load
      for (let i = 0; i < 50; i++) {
        await buffer.addItem(`stress_key_${i}`, `stress_value_${i}`);
      }

      expect(buffer.getSize()).toBe(maxSize);
      expect(buffer.isHealthy()).toBe(true);

      // Phase 2: Mixed operations under stress
      const stressPromises: Promise<void>[] = [];
      
      for (let i = 0; i < 20; i++) {
        stressPromises.push(buffer.addItem(`stress_concurrent_${i}`, `concurrent_value_${i}`));
      }

      await Promise.all(stressPromises);

      // Phase 3: Recovery verification
      expect(buffer.getSize()).toBe(maxSize);
      expect(buffer.isHealthy()).toBe(true);

      // Should still be able to perform normal operations
      await buffer.addItem('recovery_test', 'recovery_value');
      expect(buffer.getItem('recovery_test')).toBe('recovery_value');

      const finalMetrics = buffer.getMetrics();
      expect(finalMetrics.totalOperations).toBeGreaterThan(50);
    });
  });

  describe('Cross-Component Integration', () => {
    it('should work with multiple buffer instances', async () => {
      const buffer2 = new AtomicCircularBuffer<string>(5);
      const buffer3 = new AtomicCircularBuffer<number>(8);

      try {
        await buffer2.initialize();
        await buffer3.initialize();

        // Add data to all buffers
        await buffer.addItem('main_key', 'main_value');
        await buffer2.addItem('second_key', 'second_value');
        await buffer3.addItem('third_key', 123);

        // Verify independence
        expect(buffer.getItem('main_key')).toBe('main_value');
        expect(buffer2.getItem('second_key')).toBe('second_value');
        expect(buffer3.getItem('third_key')).toBe(123);

        // Cross-buffer operations should not interfere
        expect(buffer.getItem('second_key')).toBeUndefined();
        expect(buffer2.getItem('main_key')).toBeUndefined();

      } finally {
        await buffer2.shutdown();
        await buffer3.shutdown();
      }
    });
  });
});
