/**
 * @file AtomicCircularBuffer Basic Test Suite
 * @component atomic-circular-buffer-basic-tests
 * @authority-level critical-memory-safety-testing
 * @governance-adr ADR-security-002-atomic-operations-testing
 */

// Configure Jest timeout
jest.setTimeout(10000); // 10 second timeout

// CRITICAL: Rely on global Jest setup mocking (jest.setup.js) for timer and module mocking
// This ensures mocks are applied before ANY module loading occurs (Lesson 04 pattern)

console.log('[TEST] AtomicCircularBuffer.basic.test.ts starting - relying on global mocks');

// Import AFTER global mocks are established (Lesson 04 pattern)
import { AtomicCircularBuffer } from '../AtomicCircularBuffer';

describe('AtomicCircularBuffer - Basic Functionality', () => {
  let buffer: AtomicCircularBuffer<string>;
  const maxSize = 3; // Smaller size for faster tests

  beforeAll(async () => {
    console.log('[TEST] beforeAll: Setting up test environment');
    // Set test environment
    process.env.NODE_ENV = 'test';

    // Clear any existing global state (Lesson 05 pattern)
    const { MemorySafeResourceManager } = await import('../MemorySafeResourceManager');
    MemorySafeResourceManager.forceGlobalCleanup();
    console.log('[TEST] beforeAll: Global cleanup completed');
  });

  beforeEach(async () => {
    console.log('[TEST] beforeEach: Creating AtomicCircularBuffer instance');
    buffer = new AtomicCircularBuffer<string>(maxSize);
    console.log('[TEST] beforeEach: Initializing buffer');
    await buffer.initialize();
    console.log('[TEST] beforeEach: Buffer initialized successfully');
  });

  afterEach(async () => {
    console.log('[TEST] afterEach: Starting cleanup');
    try {
      if (buffer && !buffer.isShuttingDown()) {
        console.log('[TEST] afterEach: Shutting down buffer');
        await buffer.shutdown();
        console.log('[TEST] afterEach: Buffer shutdown completed');
      }
    } catch (error) {
      console.warn('[TEST] afterEach: Error during buffer shutdown:', error);
    }

    // Clear Jest timers and mocks (Lesson 05 pattern)
    jest.clearAllTimers();
    jest.clearAllMocks();

    // Single GC cycle to prevent timeout (Lesson 03 pattern)
    if (typeof (global as any).gc === 'function') {
      (global as any).gc();
    }
    console.log('[TEST] afterEach: Cleanup completed');
  });

  afterAll(async () => {
    console.log('[TEST] afterAll: Starting final cleanup');
    try {
      // Force global cleanup following Lesson 05 patterns
      const { MemorySafeResourceManager } = await import('../MemorySafeResourceManager');
      MemorySafeResourceManager.forceGlobalCleanup();

      // Clear all Jest state
      jest.clearAllTimers();
      jest.clearAllMocks();
      jest.restoreAllMocks();

      // Single GC cycle (Lesson 05 pattern)
      if (typeof (global as any).gc === 'function') {
        (global as any).gc();
      }

      // Brief wait for cleanup completion (Lesson 05 pattern)
      await new Promise(resolve => setTimeout(resolve, 50));
      console.log('[TEST] afterAll: Final cleanup completed');
    } catch (error) {
      console.error('[TEST] afterAll: Global cleanup error:', error);
    }
  });

  describe('Basic Operations', () => {
    it('should create buffer with correct initial state', () => {
      expect(buffer.getSize()).toBe(0);
      expect(buffer.getAllItems().size).toBe(0);
    });

    it('should add items correctly', async () => {
      await buffer.addItem('key1', 'value1');
      expect(buffer.getSize()).toBe(1);
      expect(buffer.getItem('key1')).toBe('value1');
    });

    it('should remove items correctly', async () => {
      await buffer.addItem('key1', 'value1');
      const removed = await buffer.removeItem('key1');
      
      expect(removed).toBe(true);
      expect(buffer.getSize()).toBe(0);
      expect(buffer.getItem('key1')).toBeUndefined();
    });

    it('should handle buffer overflow', async () => {
      // Fill buffer to capacity
      await buffer.addItem('key1', 'value1');
      await buffer.addItem('key2', 'value2');
      await buffer.addItem('key3', 'value3');
      expect(buffer.getSize()).toBe(maxSize);

      // Add one more item - should remove oldest
      await buffer.addItem('key4', 'value4');
      expect(buffer.getSize()).toBe(maxSize);
      expect(buffer.getItem('key1')).toBeUndefined(); // Oldest removed
      expect(buffer.getItem('key4')).toBe('value4'); // Newest added
    });

    it('should clear buffer correctly', async () => {
      await buffer.addItem('key1', 'value1');
      await buffer.addItem('key2', 'value2');
      expect(buffer.getSize()).toBe(2);

      await buffer.clear();
      expect(buffer.getSize()).toBe(0);
      expect(buffer.getAllItems().size).toBe(0);
    });
  });

  describe('Metrics Tracking', () => {
    it('should track basic metrics', async () => {
      const initialMetrics = buffer.getMetrics();
      expect(initialMetrics.totalOperations).toBe(0);
      expect(initialMetrics.addOperations).toBe(0);
      expect(initialMetrics.removeOperations).toBe(0);

      await buffer.addItem('test', 'value');
      await buffer.removeItem('test');

      const finalMetrics = buffer.getMetrics();
      expect(finalMetrics.totalOperations).toBe(2);
      expect(finalMetrics.addOperations).toBe(1);
      expect(finalMetrics.removeOperations).toBe(1);
    });
  });

  describe('Logging Interface', () => {
    it('should implement logging methods', () => {
      expect(typeof buffer.logInfo).toBe('function');
      expect(typeof buffer.logWarning).toBe('function');
      expect(typeof buffer.logError).toBe('function');
      expect(typeof buffer.logDebug).toBe('function');

      // Should not throw when called
      expect(() => buffer.logInfo('Test message')).not.toThrow();
      expect(() => buffer.logWarning('Test warning')).not.toThrow();
      expect(() => buffer.logError('Test error', new Error('test'))).not.toThrow();
      expect(() => buffer.logDebug('Test debug')).not.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('should handle non-existent key removal', async () => {
      const removed = await buffer.removeItem('nonexistent');
      expect(removed).toBe(false);
    });

    it('should handle duplicate key addition', async () => {
      await buffer.addItem('duplicate', 'value1');
      expect(buffer.getSize()).toBe(1);

      await buffer.addItem('duplicate', 'value2');
      expect(buffer.getSize()).toBe(1); // Size should not increase
      expect(buffer.getItem('duplicate')).toBe('value2'); // Value should be updated
    });

    it('should handle shutdown gracefully', async () => {
      await buffer.addItem('test', 'value');
      expect(buffer.getSize()).toBe(1);

      await buffer.shutdown();
      expect(buffer.isShuttingDown()).toBe(true);
      expect(buffer.getSize()).toBe(0);
    });
  });

  describe('Resource Management', () => {
    it('should integrate with MemorySafeResourceManager', () => {
      expect(buffer.isHealthy()).toBe(true);
      
      const metrics = buffer.getResourceMetrics();
      expect(metrics).toBeDefined();
      expect(typeof metrics.totalResources).toBe('number');
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle basic concurrent operations', async () => {
      const promises: Promise<void>[] = [];
      
      // Add items concurrently
      for (let i = 0; i < 10; i++) {
        promises.push(buffer.addItem(`concurrent${i}`, `value${i}`));
      }
      
      await Promise.all(promises);
      
      // Size should not exceed maximum
      expect(buffer.getSize()).toBeLessThanOrEqual(maxSize);
    });
  });
});
