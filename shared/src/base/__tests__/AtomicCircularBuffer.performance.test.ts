/**
 * @file AtomicCircularBuffer Performance Test Suite
 * @component atomic-circular-buffer-performance-tests
 * @authority-level critical-memory-safety-testing
 * @governance-adr ADR-security-002-performance-testing
 */

// Configure Jest timeout for performance tests
jest.setTimeout(20000); // 20 second timeout

// Mock timer functions to prevent real timer creation
const mockSetInterval = jest.fn((_callback: Function, ms: number) => {
  console.log(`[MOCK] setInterval called with ${ms}ms - MOCKED, NO REAL INTERVAL CREATED`);
  return 'mock-interval-id';
});

const mockClearInterval = jest.fn((id: any) => {
  console.log(`[MOCK] clearInterval called for ${id} - MOCKED`);
});

const mockSetTimeout = jest.fn((_callback: Function, ms: number) => {
  console.log(`[MOCK] setTimeout called with ${ms}ms - MOCKED, NO REAL TIMEOUT CREATED`);
  return 'mock-timeout-id';
});

const mockClearTimeout = jest.fn((id: any) => {
  console.log(`[MOCK] clearTimeout called for ${id} - MOCKED`);
});

// Apply global mocks
(global as any).setInterval = mockSetInterval;
(global as any).clearInterval = mockClearInterval;
(global as any).setTimeout = mockSetTimeout;
(global as any).clearTimeout = mockClearTimeout;

// CRITICAL: Mock MemorySafeResourceManager to eliminate ALL resource allocation (Lesson 04)
jest.mock('../MemorySafeResourceManager', () => {
  const mockCalls = {
    createSafeInterval: jest.fn((_callback?: any, _intervalMs?: number, _name?: string) => 'mock-interval-id'),
    createSafeTimeout: jest.fn((_callback?: any, _timeoutMs?: number, _name?: string) => 'mock-timeout-id'),
    clearSafeInterval: jest.fn((_id: string) => {}),
    clearSafeTimeout: jest.fn((_id: string) => {})
  };

  class MockMemorySafeResourceManager {
    protected _isInitialized = false;
    protected _isShuttingDown = false;
    protected _limits: any;

    constructor(limits?: any) {
      this._limits = limits || {};
      console.log('[MOCK] MemorySafeResourceManager constructor called - NO RESOURCES CREATED');
    }

    protected async doInitialize(): Promise<void> {
      console.log('[MOCK] doInitialize called - MOCKED, NO REAL INITIALIZATION');
      this._isInitialized = true;
    }

    protected async doShutdown(): Promise<void> {
      console.log('[MOCK] doShutdown called - MOCKED, NO REAL SHUTDOWN');
      this._isShuttingDown = true;
    }

    public async initialize(): Promise<void> {
      console.log('[MOCK] initialize called - MOCKED');
      await this.doInitialize();
    }

    public async shutdown(): Promise<void> {
      console.log('[MOCK] shutdown called - MOCKED');
      await this.doShutdown();
    }

    public isHealthy(): boolean {
      console.log(`[MOCK] isHealthy called - shutting down: ${this._isShuttingDown}, returning: ${!this._isShuttingDown}`);
      return !this._isShuttingDown;
    }

    public getResourceMetrics(): any {
      return {
        totalResources: 0,
        intervals: 0,
        timeouts: 0,
        cacheSize: 0,
        connections: 0
      };
    }

    protected createSafeInterval(callback?: any, intervalMs?: number, name?: string): string {
      return mockCalls.createSafeInterval(callback, intervalMs, name);
    }

    protected createSafeTimeout(callback?: any, timeoutMs?: number, name?: string): string {
      return mockCalls.createSafeTimeout(callback, timeoutMs, name);
    }

    protected clearSafeInterval(id: string): void {
      mockCalls.clearSafeInterval(id);
    }

    protected clearSafeTimeout(id: string): void {
      mockCalls.clearSafeTimeout(id);
    }

    public static forceGlobalCleanup(): void {
      console.log('[MOCK] MemorySafeResourceManager.forceGlobalCleanup() called - MOCKED');
    }

    public static getMockCalls() {
      return mockCalls;
    }
  }

  return {
    MemorySafeResourceManager: MockMemorySafeResourceManager,
    __mockCalls: mockCalls
  };
});

// Mock LoggingMixin
jest.mock('../LoggingMixin', () => {
  return {
    SimpleLogger: class MockSimpleLogger {
      private _name: string;

      constructor(name: string) {
        this._name = name;
      }

      logInfo(message: string, details?: Record<string, unknown>) {
        console.log(`[INFO] ${this._name}: ${message}`, details || '');
      }

      logWarning(message: string, details?: Record<string, unknown>) {
        console.warn(`[WARNING] ${this._name}: ${message}`, details || '');
      }

      logError(message: string, error: unknown, details?: Record<string, unknown>) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`[ERROR] ${this._name}: ${message} - ${errorMessage}`, details || '');
      }

      logDebug(message: string, details?: Record<string, unknown>) {
        if (process.env.NODE_ENV === 'development') {
          console.debug(`[DEBUG] ${this._name}: ${message}`, details || '');
        }
      }
    },
    ILoggingService: {}
  };
});

console.log('[TEST] AtomicCircularBuffer.performance.test.ts starting - relying on global mocks');

// Import AFTER global mocks are established
import { AtomicCircularBuffer } from '../AtomicCircularBuffer';

describe('AtomicCircularBuffer - Performance', () => {
  let buffer: AtomicCircularBuffer<string>;
  const maxSize = 100; // Larger size for performance tests

  beforeEach(async () => {
    console.log('[TEST] Creating new AtomicCircularBuffer for performance test');
    buffer = new AtomicCircularBuffer<string>(maxSize);
    await buffer.initialize();
    console.log('[TEST] AtomicCircularBuffer initialized successfully');
  });

  afterEach(async () => {
    console.log('[TEST] Cleaning up AtomicCircularBuffer');
    if (buffer) {
      try {
        await buffer.shutdown();
        console.log('[TEST] AtomicCircularBuffer shutdown completed');
      } catch (error) {
        console.warn('[TEST] Error during buffer shutdown:', error);
      }
    }
  });

  describe('Performance Benchmarks', () => {
    it('should maintain fast add operations under load', async () => {
      const operationCount = 100; // Reduced from 1000
      const startTime = Date.now();

      // Perform many add operations
      for (let i = 0; i < operationCount; i++) {
        await buffer.addItem(`perf_key_${i}`, `perf_value_${i}`);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;
      const operationsPerSecond = (operationCount / duration) * 1000;

      console.log(`[PERFORMANCE] Add operations: ${operationCount} in ${duration}ms (${operationsPerSecond.toFixed(2)} ops/sec)`);

      // Should complete within reasonable time (less than 5 seconds for 100 operations)
      expect(duration).toBeLessThan(5000);
      expect(operationsPerSecond).toBeGreaterThan(10); // At least 10 ops/sec
    });

    it('should maintain fast get operations under load', async () => {
      // Pre-populate buffer
      for (let i = 0; i < maxSize; i++) {
        await buffer.addItem(`get_key_${i}`, `get_value_${i}`);
      }

      const operationCount = 1000;
      const startTime = Date.now();

      // Perform many get operations
      for (let i = 0; i < operationCount; i++) {
        const key = `get_key_${i % maxSize}`;
        const value = buffer.getItem(key);
        expect(value).toBeDefined();
      }

      const endTime = Date.now();
      const duration = endTime - startTime;
      const operationsPerSecond = (operationCount / duration) * 1000;

      console.log(`[PERFORMANCE] Get operations: ${operationCount} in ${duration}ms (${operationsPerSecond.toFixed(2)} ops/sec)`);

      // Get operations should be very fast
      expect(duration).toBeLessThan(1000); // Less than 1 second
      expect(operationsPerSecond).toBeGreaterThan(500); // At least 500 ops/sec
    });

    it('should maintain fast remove operations under load', async () => {
      // Pre-populate buffer
      const itemCount = 200;
      for (let i = 0; i < itemCount; i++) {
        await buffer.addItem(`remove_key_${i}`, `remove_value_${i}`);
      }

      const startTime = Date.now();

      // Remove half the items
      for (let i = 0; i < itemCount / 2; i++) {
        await buffer.removeItem(`remove_key_${i}`);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;
      const operationsPerSecond = ((itemCount / 2) / duration) * 1000;

      console.log(`[PERFORMANCE] Remove operations: ${itemCount / 2} in ${duration}ms (${operationsPerSecond.toFixed(2)} ops/sec)`);

      // Should complete within reasonable time
      expect(duration).toBeLessThan(3000);
      expect(operationsPerSecond).toBeGreaterThan(20); // At least 20 ops/sec
    });

    it('should handle mixed operations efficiently', async () => {
      const operationCount = 300; // Mix of operations
      const startTime = Date.now();

      for (let i = 0; i < operationCount; i++) {
        const operation = i % 3;
        
        switch (operation) {
          case 0: // Add
            await buffer.addItem(`mixed_key_${i}`, `mixed_value_${i}`);
            break;
          case 1: // Get
            buffer.getItem(`mixed_key_${Math.max(0, i - 10)}`);
            break;
          case 2: // Remove
            if (i > 20) {
              await buffer.removeItem(`mixed_key_${i - 20}`);
            }
            break;
        }
      }

      const endTime = Date.now();
      const duration = endTime - startTime;
      const operationsPerSecond = (operationCount / duration) * 1000;

      console.log(`[PERFORMANCE] Mixed operations: ${operationCount} in ${duration}ms (${operationsPerSecond.toFixed(2)} ops/sec)`);

      expect(duration).toBeLessThan(8000); // Less than 8 seconds
      expect(operationsPerSecond).toBeGreaterThan(15); // At least 15 ops/sec
    });
  });

  describe('Concurrent Access Performance', () => {
    it('should handle basic concurrent operations', async () => {
      const promises: Promise<void>[] = [];
      
      // Add items concurrently
      for (let i = 0; i < 10; i++) {
        promises.push(buffer.addItem(`concurrent${i}`, `value${i}`));
      }
      
      const startTime = Date.now();
      await Promise.all(promises);
      const duration = Date.now() - startTime;

      console.log(`[PERFORMANCE] Concurrent operations completed in ${duration}ms`);

      // Size should not exceed maximum
      expect(buffer.getSize()).toBeLessThanOrEqual(maxSize);
      expect(duration).toBeLessThan(2000); // Should complete quickly
    });

    it('should handle concurrent additions without corruption', async () => {
      const concurrentPromises: Promise<void>[] = [];
      const itemCount = 20;
      const startTime = Date.now();

      // Launch multiple concurrent add operations
      for (let i = 0; i < itemCount; i++) {
        concurrentPromises.push(
          buffer.addItem(`concurrent_add_${i}`, `concurrent_value_${i}`)
        );
      }

      await Promise.all(concurrentPromises);
      const duration = Date.now() - startTime;

      console.log(`[PERFORMANCE] Concurrent additions: ${itemCount} items in ${duration}ms`);

      // Verify final state
      expect(buffer.getSize()).toBeLessThanOrEqual(maxSize);
      expect(buffer.getSize()).toBeGreaterThan(0);
      expect(duration).toBeLessThan(3000);
    });

    it('should handle rapid key updates without data loss', async () => {
      const key = 'race_key';
      const updateCount = 20; // Reduced from 100 for faster execution
      const promises: Promise<void>[] = [];
      const startTime = Date.now();

      // Launch concurrent updates to the same key
      for (let i = 0; i < updateCount; i++) {
        promises.push(
          buffer.addItem(key, `race_value_${i}`)
        );
      }

      await Promise.all(promises);
      const duration = Date.now() - startTime;

      console.log(`[PERFORMANCE] Rapid key updates: ${updateCount} updates in ${duration}ms`);

      // Key should exist with some value
      const finalValue = buffer.getItem(key);
      expect(finalValue).toBeDefined();
      expect(finalValue).toMatch(/^race_value_\d+$/);
      expect(buffer.getSize()).toBe(1);
      expect(duration).toBeLessThan(2000);
    });
  });

  describe('Stress Testing', () => {
    it('should handle high-concurrency stress test', async () => {
      const concurrencyLevel = 10; // Reduced from 50
      const operationsPerThread = 5; // Reduced from 20
      const startTime = Date.now();

      const threadPromises: Promise<void>[] = [];

      for (let thread = 0; thread < concurrencyLevel; thread++) {
        const threadPromise = (async () => {
          for (let op = 0; op < operationsPerThread; op++) {
            const key = `stress_t${thread}_op${op}`;
            const value = `stress_value_t${thread}_op${op}`;
            
            await buffer.addItem(key, value);
            
            // Occasionally read and remove
            if (op % 2 === 0) {
              buffer.getItem(key);
            }
            if (op % 3 === 0 && op > 0) {
              await buffer.removeItem(`stress_t${thread}_op${op - 1}`);
            }
          }
        })();
        
        threadPromises.push(threadPromise);
      }

      await Promise.all(threadPromises);
      const duration = Date.now() - startTime;
      const totalOperations = concurrencyLevel * operationsPerThread * 2; // Approximate

      console.log(`[PERFORMANCE] Stress test: ${totalOperations} operations across ${concurrencyLevel} threads in ${duration}ms`);

      // Buffer should still be functional
      expect(buffer.getSize()).toBeLessThanOrEqual(maxSize);
      expect(buffer.isHealthy()).toBe(true);
      expect(duration).toBeLessThan(10000); // Should complete within 10 seconds
    });

    it('should maintain performance with large datasets', async () => {
      const largeBuffer = new AtomicCircularBuffer<string>(1000);
      await largeBuffer.initialize();

      try {
        const itemCount = 2000; // More items than buffer capacity
        const startTime = Date.now();

        // Add many items
        for (let i = 0; i < itemCount; i++) {
          await largeBuffer.addItem(`large_key_${i}`, `large_value_${i}`);
        }

        const duration = Date.now() - startTime;
        const operationsPerSecond = (itemCount / duration) * 1000;

        console.log(`[PERFORMANCE] Large dataset: ${itemCount} items in ${duration}ms (${operationsPerSecond.toFixed(2)} ops/sec)`);

        // Should maintain reasonable performance
        expect(largeBuffer.getSize()).toBe(1000); // Should be at capacity
        expect(duration).toBeLessThan(15000); // Should complete within 15 seconds
        expect(operationsPerSecond).toBeGreaterThan(50); // At least 50 ops/sec

      } finally {
        await largeBuffer.shutdown();
      }
    });
  });

  describe('Performance Metrics', () => {
    it('should track operation metrics accurately', async () => {
      const operationCount = 50;
      
      // Perform operations
      for (let i = 0; i < operationCount; i++) {
        await buffer.addItem(`metrics_key_${i}`, `metrics_value_${i}`);
      }

      // Remove some items
      for (let i = 0; i < operationCount / 2; i++) {
        await buffer.removeItem(`metrics_key_${i}`);
      }

      const metrics = buffer.getMetrics();
      
      expect(metrics.addOperations).toBe(operationCount);
      expect(metrics.removeOperations).toBe(operationCount / 2);
      expect(metrics.totalOperations).toBe(operationCount + operationCount / 2);
    });

    it('should provide performance insights through metrics', async () => {
      const startTime = Date.now();
      
      // Perform various operations
      for (let i = 0; i < 30; i++) {
        await buffer.addItem(`insight_key_${i}`, `insight_value_${i}`);
        if (i % 5 === 0) {
          buffer.getItem(`insight_key_${i}`);
        }
      }

      const duration = Date.now() - startTime;
      const metrics = buffer.getMetrics();

      console.log(`[PERFORMANCE] Metrics insights: ${metrics.totalOperations} operations in ${duration}ms`);

      expect(metrics.totalOperations).toBeGreaterThan(0);
      expect(metrics.addOperations).toBe(30);
    });
  });
});
