/**
 * @file Memory Safe System Integration Test Suite
 * @component memory-safe-system-integration-tests
 * @authority-level critical-memory-safety
 * 
 * 🚨 PHASE 5: Integration and System-Wide Testing
 * 
 * Comprehensive integration tests validating all four components working together:
 * - EventHandlerRegistry (Phase 1) - 41/41 tests passing
 * - MemorySafeResourceManager (Phase 2) - Memory-safe resource management
 * - TimerCoordinationService (Phase 3) - Timer coordination and cleanup
 * - CleanupCoordinator (Phase 4) - 17/17 tests passing, operation coordination
 * 
 * Integration scenarios:
 * - Cross-component communication and dependencies
 * - Coordinated cleanup during application shutdown
 * - Memory leak prevention across component boundaries
 * - Real-world usage patterns and stress testing
 * - Error conditions and recovery across components
 */

import {
  MemorySafetyManager,
  getMemorySafetyManager,
  resetMemorySafetyManager,
  ShutdownPhase
} from '../MemorySafetyManager';

import { getEventHandlerRegistry } from '../EventHandlerRegistry';
import { getTimerCoordinator } from '../TimerCoordinationService';
import { getCleanupCoordinator, CleanupOperationType, CleanupPriority } from '../CleanupCoordinator';

// Mock console for testing
const mockConsole = {
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  info: jest.fn()
};

// Configure Jest timeout for integration tests
jest.setTimeout(15000); // 15 second timeout for integration tests

describe('Memory Safe System Integration Tests', () => {
  let memorySafetyManager: MemorySafetyManager;

  beforeAll(() => {
    // Use fake timers for controlled testing
    jest.useFakeTimers();
  });

  afterAll(() => {
    // Restore real timers
    jest.useRealTimers();
  });

  beforeEach(async () => {
    // Reset all components
    resetMemorySafetyManager();

    // Reset console mocks
    Object.values(mockConsole).forEach(mock => mock.mockClear());

    // Override console methods for logging tests
    global.console = {
      ...global.console,
      ...mockConsole
    };

    // Create fresh memory safety manager with test configuration
    memorySafetyManager = getMemorySafetyManager({
      eventHandlerConfig: {
        maxHandlersPerClient: 50,
        maxGlobalHandlers: 1000,
        cleanupIntervalMs: 1000
      },
      resourceManagerConfig: {
        maxIntervals: 5,
        maxTimeouts: 5,
        maxCacheSize: 10 * 1024 * 1024, // 10MB
        memoryThresholdMB: 50
      },
      timerCoordinationConfig: {
        maxConcurrentTimers: 20,
        defaultTimeoutMs: 5000,
        cleanupIntervalMs: 1000
      },
      cleanupCoordinatorConfig: {
        maxConcurrentOperations: 3,
        defaultTimeout: 5000,
        maxRetries: 2,
        conflictDetectionEnabled: true,
        testMode: true // Enable test mode for timer compatibility
      },
      shutdownTimeoutMs: 10000,
      emergencyCleanupEnabled: true,
      performanceMonitoringEnabled: true,
      memoryLeakDetectionEnabled: true
    });

    await memorySafetyManager.initialize();
  });

  afterEach(async () => {
    if (memorySafetyManager) {
      await memorySafetyManager.shutdown();
    }
    resetMemorySafetyManager();
  });

  describe('Cross-Component Integration', () => {
    it('should initialize all components successfully', async () => {
      // Verify all components are initialized
      const metrics = await memorySafetyManager.getSystemMetrics();
      
      expect(metrics).toBeDefined();
      expect(metrics.eventHandlers).toBeDefined();
      expect(metrics.resources).toBeDefined();
      expect(metrics.timers).toBeDefined();
      expect(metrics.cleanup).toBeDefined();
      
      // System should be healthy after initialization
      expect(metrics.systemHealthScore).toBeGreaterThan(80);
    });

    it('should coordinate operations across all components', async () => {
      const eventRegistry = getEventHandlerRegistry();
      const timerCoordinator = getTimerCoordinator();
      const cleanupCoordinator = getCleanupCoordinator();

      // Register event handlers
      const clientId = 'integration-test-client';
      const handlerId1 = eventRegistry.registerHandler(clientId, 'test-event-1', () => {});
      const handlerId2 = eventRegistry.registerHandler(clientId, 'test-event-2', () => {});

      // Create coordinated timers
      let timerExecuted = false;
      timerCoordinator.createCoordinatedTimer(
        () => { timerExecuted = true; },
        1000,
        'integration-timer'
      );

      // Schedule cleanup operations
      let cleanupExecuted = false;
      cleanupCoordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'integration-cleanup',
        async () => { cleanupExecuted = true; }
      );

      // Process operations
      await cleanupCoordinator.processQueue();
      await cleanupCoordinator.waitForCompletion();

      // Advance timers
      jest.advanceTimersByTime(1100);
      await Promise.resolve();

      // Verify operations executed
      expect(timerExecuted).toBe(true);
      expect(cleanupExecuted).toBe(true);

      // Verify handlers are registered
      expect(eventRegistry.getHandlerCount(clientId)).toBe(2);

      // Get final metrics
      const metrics = await memorySafetyManager.getSystemMetrics();
      expect(metrics.eventHandlers.totalHandlers).toBe(2);
      expect(metrics.cleanup.totalOperations).toBeGreaterThan(0);
    });

    it('should handle cross-component dependencies', async () => {
      const cleanupCoordinator = getCleanupCoordinator();
      const executionOrder: string[] = [];

      // Schedule dependent operations across components
      const dependencyId = cleanupCoordinator.scheduleCleanup(
        CleanupOperationType.TIMER_CLEANUP,
        'timer-dependency',
        async () => {
          executionOrder.push('timer-cleanup');
        }
      );

      cleanupCoordinator.scheduleCleanup(
        CleanupOperationType.EVENT_HANDLER_CLEANUP,
        'event-dependent',
        async () => {
          executionOrder.push('event-cleanup');
        },
        { dependencies: [dependencyId] }
      );

      // Process operations
      await cleanupCoordinator.processQueue();
      await cleanupCoordinator.waitForCompletion();

      // Verify execution order respects dependencies
      expect(executionOrder).toEqual(['timer-cleanup', 'event-cleanup']);
    });
  });

  describe('System-Wide Memory Leak Prevention', () => {
    it('should prevent memory leaks during normal operations', async () => {
      const initialMetrics = await memorySafetyManager.getSystemMetrics();
      const eventRegistry = getEventHandlerRegistry();
      const timerCoordinator = getTimerCoordinator();

      // Perform multiple operations that could cause memory leaks
      for (let i = 0; i < 10; i++) {
        const clientId = `client-${i}`;
        
        // Register and unregister event handlers
        const handlerId = eventRegistry.registerHandler(clientId, 'test-event', () => {});
        eventRegistry.unregisterHandler(clientId, handlerId);
        
        // Create and clear timers
        const timerId = timerCoordinator.createCoordinatedTimer(
          () => {},
          100,
          `timer-${i}`
        );
        timerCoordinator.clearCoordinatedTimer(timerId);
      }

      // Force cleanup
      await memorySafetyManager.forceCleanup();

      const finalMetrics = await memorySafetyManager.getSystemMetrics();
      
      // Memory usage should not have increased significantly
      const memoryIncrease = finalMetrics.totalMemoryUsageBytes - initialMetrics.totalMemoryUsageBytes;
      expect(memoryIncrease).toBeLessThan(1024 * 1024); // Less than 1MB increase
      
      // No handlers should remain
      expect(finalMetrics.eventHandlers.totalHandlers).toBe(0);
      
      // System health should remain high
      expect(finalMetrics.systemHealthScore).toBeGreaterThan(70);
    });

    it('should detect and handle memory leaks', async () => {
      const eventRegistry = getEventHandlerRegistry();
      
      // Create a scenario that could cause memory leaks
      const clientId = 'memory-leak-client';
      for (let i = 0; i < 100; i++) {
        eventRegistry.registerHandler(clientId, `event-${i}`, () => {});
      }

      // Trigger memory leak detection
      memorySafetyManager.updateMetrics();

      const metrics = await memorySafetyManager.getSystemMetrics();
      
      // Should detect high handler count
      expect(metrics.eventHandlers.totalHandlers).toBe(100);
      
      // System health should be impacted
      expect(metrics.systemHealthScore).toBeLessThan(100);
    });
  });

  describe('Coordinated Shutdown Procedures', () => {
    it('should execute coordinated shutdown across all components', async () => {
      const eventRegistry = getEventHandlerRegistry();
      const timerCoordinator = getTimerCoordinator();
      const cleanupCoordinator = getCleanupCoordinator();

      // Setup active operations across all components
      const clientId = 'shutdown-test-client';
      eventRegistry.registerHandler(clientId, 'test-event', () => {});
      
      timerCoordinator.createCoordinatedTimer(() => {}, 5000, 'shutdown-timer');
      
      cleanupCoordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'shutdown-cleanup',
        async () => {
          // Simulate work
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      );

      // Execute coordinated shutdown
      const shutdownPromise = memorySafetyManager.shutdown();
      
      // Advance timers to allow shutdown to complete
      jest.advanceTimersByTime(15000);
      await Promise.resolve();
      await Promise.resolve();
      
      await shutdownPromise;

      // Verify all components are cleaned up
      const finalMetrics = await memorySafetyManager.getSystemMetrics();
      expect(finalMetrics.resources.activeIntervals).toBe(0);
      expect(finalMetrics.resources.activeTimeouts).toBe(0);
      expect(finalMetrics.cleanup.runningOperations).toBe(0);
    });

    it('should handle shutdown timeout gracefully', async () => {
      const cleanupCoordinator = getCleanupCoordinator();

      // Schedule a long-running operation
      cleanupCoordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'long-running-cleanup',
        async () => {
          // Simulate a stuck operation
          await new Promise(resolve => setTimeout(resolve, 20000)); // 20 seconds
        }
      );

      // Start shutdown with shorter timeout
      const shutdownStart = Date.now();
      await memorySafetyManager.shutdown();
      const shutdownTime = Date.now() - shutdownStart;

      // Should complete within reasonable time due to timeout
      expect(shutdownTime).toBeLessThan(15000); // Less than 15 seconds
      
      // Should log warnings about timeout
      expect(mockConsole.warn).toHaveBeenCalledWith(
        expect.stringContaining('Timeout waiting for operations to complete'),
        expect.any(String)
      );
    });
  });

  describe('Error Conditions and Recovery', () => {
    it('should handle component failures gracefully', async () => {
      const cleanupCoordinator = getCleanupCoordinator();

      // Schedule operations that will fail
      let failureCount = 0;
      cleanupCoordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'failing-operation-1',
        async () => {
          failureCount++;
          throw new Error('Simulated failure 1');
        },
        { maxRetries: 2 }
      );

      cleanupCoordinator.scheduleCleanup(
        CleanupOperationType.TIMER_CLEANUP,
        'failing-operation-2',
        async () => {
          failureCount++;
          throw new Error('Simulated failure 2');
        },
        { maxRetries: 1 }
      );

      // Process operations
      await cleanupCoordinator.processQueue();
      await cleanupCoordinator.waitForCompletion();

      // Should have attempted retries
      expect(failureCount).toBeGreaterThan(2);

      // System should still be functional
      const metrics = await memorySafetyManager.getSystemMetrics();
      expect(metrics.systemHealthScore).toBeGreaterThan(50); // Reduced but still functional
    });

    it('should recover from emergency cleanup scenarios', async () => {
      const eventRegistry = getEventHandlerRegistry();
      const timerCoordinator = getTimerCoordinator();

      // Create a high-load scenario
      const clientId = 'emergency-client';
      for (let i = 0; i < 50; i++) {
        eventRegistry.registerHandler(clientId, `emergency-event-${i}`, () => {});
        timerCoordinator.createCoordinatedTimer(() => {}, 1000, `emergency-timer-${i}`);
      }

      // Trigger emergency cleanup
      await memorySafetyManager.forceCleanup();

      // Advance timers to complete cleanup
      jest.advanceTimersByTime(2000);
      await Promise.resolve();

      const metrics = await memorySafetyManager.getSystemMetrics();

      // Should have cleaned up most resources
      expect(metrics.resources.activeIntervals).toBeLessThan(10);
      expect(metrics.resources.activeTimeouts).toBeLessThan(10);

      // System should recover
      expect(metrics.systemHealthScore).toBeGreaterThan(60);
    });
  });

  describe('Performance Impact Validation', () => {
    it('should maintain low performance overhead', async () => {
      const performanceStart = process.hrtime.bigint();

      // Perform standard operations
      const eventRegistry = getEventHandlerRegistry();
      const timerCoordinator = getTimerCoordinator();
      const cleanupCoordinator = getCleanupCoordinator();

      // Simulate realistic workload
      for (let i = 0; i < 20; i++) {
        const clientId = `perf-client-${i}`;
        eventRegistry.registerHandler(clientId, 'perf-event', () => {});

        timerCoordinator.createCoordinatedTimer(() => {}, 100, `perf-timer-${i}`);

        cleanupCoordinator.scheduleCleanup(
          CleanupOperationType.RESOURCE_CLEANUP,
          `perf-cleanup-${i}`,
          async () => {}
        );
      }

      // Process operations
      await cleanupCoordinator.processQueue();
      await cleanupCoordinator.waitForCompletion();

      const performanceEnd = process.hrtime.bigint();
      const executionTime = Number(performanceEnd - performanceStart) / 1000000; // Convert to milliseconds

      // Should complete within reasonable time
      expect(executionTime).toBeLessThan(1000); // Less than 1 second

      const metrics = await memorySafetyManager.getSystemMetrics();

      // Performance overhead should be minimal
      expect(metrics.performanceOverhead).toBeLessThan(5); // Less than 5%
    });

    it('should scale efficiently with increased load', async () => {
      const loadSizes = [10, 50, 100];
      const executionTimes: number[] = [];

      for (const loadSize of loadSizes) {
        const startTime = process.hrtime.bigint();

        const eventRegistry = getEventHandlerRegistry();
        const cleanupCoordinator = getCleanupCoordinator();

        // Create load
        for (let i = 0; i < loadSize; i++) {
          eventRegistry.registerHandler(`load-client-${i}`, 'load-event', () => {});
          cleanupCoordinator.scheduleCleanup(
            CleanupOperationType.RESOURCE_CLEANUP,
            `load-cleanup-${i}`,
            async () => {}
          );
        }

        // Process operations
        await cleanupCoordinator.processQueue();
        await cleanupCoordinator.waitForCompletion();

        const endTime = process.hrtime.bigint();
        const executionTime = Number(endTime - startTime) / 1000000;
        executionTimes.push(executionTime);

        // Clean up for next iteration
        eventRegistry.unregisterAllHandlers(`load-client-${loadSize - 1}`);
      }

      // Execution time should scale reasonably (not exponentially)
      const scalingFactor = executionTimes[2] / executionTimes[0]; // 100 vs 10 items
      expect(scalingFactor).toBeLessThan(15); // Should not be more than 15x slower for 10x load
    });
  });

  describe('Real-World Usage Patterns', () => {
    it('should handle web server simulation', async () => {
      const eventRegistry = getEventHandlerRegistry();
      const timerCoordinator = getTimerCoordinator();
      const cleanupCoordinator = getCleanupCoordinator();

      // Simulate web server with multiple clients
      const clients = ['user-1', 'user-2', 'user-3', 'user-4', 'user-5'];
      const activeConnections: string[] = [];

      for (const clientId of clients) {
        // Register connection handlers
        eventRegistry.registerHandler(clientId, 'connection', () => {
          activeConnections.push(clientId);
        });

        eventRegistry.registerHandler(clientId, 'disconnect', () => {
          const index = activeConnections.indexOf(clientId);
          if (index > -1) activeConnections.splice(index, 1);
        });

        // Setup session timeout
        timerCoordinator.createCoordinatedTimer(
          () => {
            // Simulate session cleanup
            cleanupCoordinator.scheduleCleanup(
              CleanupOperationType.RESOURCE_CLEANUP,
              `session-cleanup-${clientId}`,
              async () => {
                eventRegistry.unregisterAllHandlers(clientId);
              }
            );
          },
          5000,
          `session-timeout-${clientId}`
        );
      }

      // Simulate some activity
      jest.advanceTimersByTime(1000);
      await Promise.resolve();

      // Process cleanup operations
      await cleanupCoordinator.processQueue();
      await cleanupCoordinator.waitForCompletion();

      const metrics = await memorySafetyManager.getSystemMetrics();

      // Should handle multiple clients efficiently
      expect(metrics.eventHandlers.totalHandlers).toBe(10); // 2 handlers per client
      expect(metrics.systemHealthScore).toBeGreaterThan(80);

      // Advance to trigger session timeouts
      jest.advanceTimersByTime(6000);
      await cleanupCoordinator.processQueue();
      await cleanupCoordinator.waitForCompletion();

      const finalMetrics = await memorySafetyManager.getSystemMetrics();

      // Sessions should be cleaned up
      expect(finalMetrics.eventHandlers.totalHandlers).toBe(0);
    });

    it('should handle high-frequency operations', async () => {
      const cleanupCoordinator = getCleanupCoordinator();
      let operationsCompleted = 0;

      // Schedule many small operations
      for (let i = 0; i < 100; i++) {
        cleanupCoordinator.scheduleCleanup(
          CleanupOperationType.RESOURCE_CLEANUP,
          `high-freq-${i}`,
          async () => {
            operationsCompleted++;
          },
          { priority: i % 2 === 0 ? CleanupPriority.HIGH : CleanupPriority.LOW }
        );
      }

      // Process all operations
      await cleanupCoordinator.processQueue();
      await cleanupCoordinator.waitForCompletion();

      // All operations should complete
      expect(operationsCompleted).toBe(100);

      const metrics = await memorySafetyManager.getSystemMetrics();
      expect(metrics.cleanup.totalOperations).toBe(100);
      expect(metrics.cleanup.runningOperations).toBe(0);
      expect(metrics.systemHealthScore).toBeGreaterThan(70);
    });
  });
});
