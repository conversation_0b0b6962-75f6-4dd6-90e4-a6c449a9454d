/**
 * @file AtomicCircularBuffer Core Test Suite
 * @component atomic-circular-buffer-core-tests
 * @authority-level critical-memory-safety-testing
 * @governance-adr ADR-security-002-atomic-operations-testing
 */

// Configure Jest timeout
jest.setTimeout(10000); // 10 second timeout

// CRITICAL: Rely on global Jest setup mocking (jest.setup.js) for timer and module mocking
// This ensures mocks are applied before ANY module loading occurs (Lesson 04 pattern)

console.log('[TEST] AtomicCircularBuffer.core.test.ts starting - relying on global mocks');

// Import AFTER global mocks are established (Lesson 04 pattern)
import { AtomicCircularBuffer } from '../AtomicCircularBuffer';

describe('AtomicCircularBuffer - Core Functionality', () => {
  let buffer: AtomicCircularBuffer<string>;
  const maxSize = 5;

  beforeEach(async () => {
    console.log('[TEST] Creating new AtomicCircularBuffer for test');
    buffer = new AtomicCircularBuffer<string>(maxSize);
    await buffer.initialize();
    console.log('[TEST] AtomicCircularBuffer initialized successfully');
  });

  afterEach(async () => {
    console.log('[TEST] Cleaning up AtomicCircularBuffer');
    if (buffer) {
      try {
        await buffer.shutdown();
        console.log('[TEST] AtomicCircularBuffer shutdown completed');
      } catch (error) {
        console.warn('[TEST] Error during buffer shutdown:', error);
      }
    }
  }, 10000); // 10 second timeout for cleanup

  describe('Constructor and Initialization', () => {
    it('should create buffer with valid max size', () => {
      const testBuffer = new AtomicCircularBuffer<string>(10);
      expect(testBuffer).toBeInstanceOf(AtomicCircularBuffer);
      expect(testBuffer.getSize()).toBe(0);
    });

    it('should initialize with empty state', () => {
      expect(buffer.getSize()).toBe(0);
      expect(buffer.getAllItems().size).toBe(0);
    });

    it('should handle zero max size', () => {
      const zeroBuffer = new AtomicCircularBuffer<string>(0);
      expect(zeroBuffer.getSize()).toBe(0);
    });

    it('should handle large max size', () => {
      const largeBuffer = new AtomicCircularBuffer<string>(10000);
      expect(largeBuffer.getSize()).toBe(0);
    });
  });

  describe('Basic Add Operations', () => {
    it('should add single item correctly', async () => {
      await buffer.addItem('key1', 'value1');
      expect(buffer.getSize()).toBe(1);
      expect(buffer.getItem('key1')).toBe('value1');
    });

    it('should add multiple items correctly', async () => {
      await buffer.addItem('key1', 'value1');
      await buffer.addItem('key2', 'value2');
      await buffer.addItem('key3', 'value3');
      
      expect(buffer.getSize()).toBe(3);
      expect(buffer.getItem('key1')).toBe('value1');
      expect(buffer.getItem('key2')).toBe('value2');
      expect(buffer.getItem('key3')).toBe('value3');
    });

    it('should handle duplicate keys by updating values', async () => {
      await buffer.addItem('key1', 'value1');
      await buffer.addItem('key1', 'updated_value1');
      
      expect(buffer.getSize()).toBe(1);
      expect(buffer.getItem('key1')).toBe('updated_value1');
    });

    it('should handle empty string keys', async () => {
      await buffer.addItem('', 'empty_key_value');
      expect(buffer.getItem('')).toBe('empty_key_value');
    });

    it('should handle special character keys', async () => {
      const specialKeys = ['key with spaces', 'key-with-dashes', 'key_with_underscores', 'key.with.dots'];
      
      for (const key of specialKeys) {
        await buffer.addItem(key, `value_for_${key}`);
        expect(buffer.getItem(key)).toBe(`value_for_${key}`);
      }
    });
  });

  describe('Basic Remove Operations', () => {
    beforeEach(async () => {
      // Add some test data
      await buffer.addItem('key1', 'value1');
      await buffer.addItem('key2', 'value2');
      await buffer.addItem('key3', 'value3');
    });

    it('should remove existing item correctly', async () => {
      const removed = await buffer.removeItem('key2');
      expect(removed).toBe('value2');
      expect(buffer.getSize()).toBe(2);
      expect(buffer.getItem('key2')).toBeUndefined();
    });

    it('should return undefined for non-existent key', async () => {
      const removed = await buffer.removeItem('nonexistent');
      expect(removed).toBeUndefined();
      expect(buffer.getSize()).toBe(3); // Size should remain unchanged
    });

    it('should handle removing all items', async () => {
      await buffer.removeItem('key1');
      await buffer.removeItem('key2');
      await buffer.removeItem('key3');
      
      expect(buffer.getSize()).toBe(0);
      expect(buffer.getAllItems().size).toBe(0);
    });
  });

  describe('Basic Get Operations', () => {
    beforeEach(async () => {
      await buffer.addItem('key1', 'value1');
      await buffer.addItem('key2', 'value2');
    });

    it('should retrieve existing items correctly', () => {
      expect(buffer.getItem('key1')).toBe('value1');
      expect(buffer.getItem('key2')).toBe('value2');
    });

    it('should return undefined for non-existent keys', () => {
      expect(buffer.getItem('nonexistent')).toBeUndefined();
      expect(buffer.getItem('')).toBeUndefined();
      expect(buffer.getItem('null')).toBeUndefined();
    });

    it('should handle getAllItems correctly', () => {
      const allItems = buffer.getAllItems();
      expect(allItems).toBeInstanceOf(Map);
      expect(allItems.size).toBe(2);
      expect(allItems.get('key1')).toBe('value1');
      expect(allItems.get('key2')).toBe('value2');
    });

    it('should return defensive copy of all items', () => {
      const allItems1 = buffer.getAllItems();
      const allItems2 = buffer.getAllItems();
      
      expect(allItems1).not.toBe(allItems2); // Different instances
      expect(allItems1.size).toBe(allItems2.size);
    });
  });

  describe('Size Management', () => {
    it('should track size accurately', async () => {
      expect(buffer.getSize()).toBe(0);
      
      await buffer.addItem('key1', 'value1');
      expect(buffer.getSize()).toBe(1);
      
      await buffer.addItem('key2', 'value2');
      expect(buffer.getSize()).toBe(2);
      
      await buffer.removeItem('key1');
      expect(buffer.getSize()).toBe(1);
    });

    it('should enforce maximum size limits', async () => {
      // Add items up to max size
      for (let i = 0; i < maxSize; i++) {
        await buffer.addItem(`key${i}`, `value${i}`);
      }
      expect(buffer.getSize()).toBe(maxSize);
      
      // Add one more item - should trigger circular behavior
      await buffer.addItem('overflow', 'overflow_value');
      expect(buffer.getSize()).toBe(maxSize); // Size should not exceed maximum
    });

    it('should handle size correctly with duplicate keys', async () => {
      await buffer.addItem('key1', 'value1');
      expect(buffer.getSize()).toBe(1);
      
      await buffer.addItem('key1', 'updated_value1');
      expect(buffer.getSize()).toBe(1); // Size should remain the same
    });
  });

  describe('Circular Buffer Behavior', () => {
    it('should implement circular behavior when full', async () => {
      // Fill buffer to capacity
      for (let i = 0; i < maxSize; i++) {
        await buffer.addItem(`key${i}`, `value${i}`);
      }
      
      // Add one more item
      await buffer.addItem('overflow', 'overflow_value');
      
      // First item should be removed (circular behavior)
      expect(buffer.getItem('key0')).toBeUndefined();
      expect(buffer.getItem('overflow')).toBe('overflow_value');
      expect(buffer.getSize()).toBe(maxSize);
    });

    it('should maintain insertion order in circular behavior', async () => {
      // Fill buffer
      for (let i = 0; i < maxSize; i++) {
        await buffer.addItem(`key${i}`, `value${i}`);
      }
      
      // Add more items to trigger multiple circular removals
      await buffer.addItem('overflow1', 'overflow_value1');
      await buffer.addItem('overflow2', 'overflow_value2');
      
      // Check that oldest items are removed
      expect(buffer.getItem('key0')).toBeUndefined();
      expect(buffer.getItem('key1')).toBeUndefined();
      expect(buffer.getItem('overflow1')).toBe('overflow_value1');
      expect(buffer.getItem('overflow2')).toBe('overflow_value2');
    });
  });

  describe('Resource Management Integration', () => {
    it('should integrate with MemorySafeResourceManager', () => {
      expect(buffer.isHealthy()).toBe(true);
      
      const metrics = buffer.getResourceMetrics();
      expect(metrics).toBeDefined();
      expect(typeof metrics.totalResources).toBe('number');
    });

    it('should handle initialization and shutdown properly', async () => {
      const testBuffer = new AtomicCircularBuffer<string>(5);
      
      // Should be able to initialize
      await expect(testBuffer.initialize()).resolves.not.toThrow();
      expect(testBuffer.isHealthy()).toBe(true);
      
      // Should be able to shutdown
      await expect(testBuffer.shutdown()).resolves.not.toThrow();
    });
  });

  describe('Basic Metrics', () => {
    it('should initialize metrics with zero values', () => {
      const metrics = buffer.getMetrics();
      expect(metrics.totalOperations).toBe(0);
      expect(metrics.addOperations).toBe(0);
      expect(metrics.removeOperations).toBe(0);
    });

    it('should track basic operations in metrics', async () => {
      await buffer.addItem('key1', 'value1');
      await buffer.addItem('key2', 'value2');
      await buffer.removeItem('key1');
      
      const metrics = buffer.getMetrics();
      expect(metrics.addOperations).toBe(2);
      expect(metrics.removeOperations).toBe(1);
      expect(metrics.totalOperations).toBe(3);
    });
  });
});
