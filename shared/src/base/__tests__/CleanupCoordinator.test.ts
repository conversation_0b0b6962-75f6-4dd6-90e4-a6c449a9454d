/**
 * @file Cleanup Coordinator Test Suite
 * @component cleanup-coordinator-tests
 * @authority-level critical-memory-safety
 * 
 * 🚨 PHASE 4: Cleanup Operation Coordination Tests
 * 
 * This test suite validates the CleanupCoordinator functionality:
 * - Concurrent cleanup operation testing
 * - Cleanup conflict prevention validation
 * - Integration testing with existing memory-safe components
 * - Performance impact assessment
 */

import {
  CleanupCoordinator,
  CleanupOperationType,
  CleanupPriority,
  CleanupStatus,
  getCleanupCoordinator,
  resetCleanupCoordinator
} from '../CleanupCoordinator';

// Mock console for testing
const mockConsole = {
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
};

// Configure Jest timeout for cleanup tests
jest.setTimeout(30000); // 30 second timeout

describe('CleanupCoordinator', () => {
  let coordinator: CleanupCoordinator;

  beforeEach(async () => {
    // Reset any existing coordinator
    resetCleanupCoordinator();

    // Reset console mocks
    Object.values(mockConsole).forEach(mock => mock.mockClear());

    // Override console methods for logging tests
    global.console = {
      ...global.console,
      ...mockConsole
    };

    // Create fresh coordinator instance with test configuration
    coordinator = getCleanupCoordinator({
      maxConcurrentOperations: 3,
      defaultTimeout: 5000,
      maxRetries: 2,
      conflictDetectionEnabled: true,
      metricsEnabled: true,
      cleanupIntervalMs: 1000
    });

    await coordinator.initialize();
  });

  afterEach(async () => {
    if (coordinator) {
      await coordinator.shutdown();
    }
    resetCleanupCoordinator();
  });

  describe('Basic Operation Management', () => {
    it('should schedule and execute cleanup operations', async () => {
      let executed = false;
      const operation = async () => {
        executed = true;
      };

      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'test-component',
        operation
      );

      expect(operationId).toBeDefined();
      expect(coordinator.getOperationStatus(operationId)).toBe(CleanupStatus.QUEUED);

      // Wait for execution
      await new Promise(resolve => setTimeout(resolve, 2000));

      expect(executed).toBe(true);
      expect(coordinator.getOperationStatus(operationId)).toBe(CleanupStatus.COMPLETED);
    });

    it('should handle operation priorities correctly', async () => {
      const executionOrder: string[] = [];

      // Schedule operations with different priorities
      const lowPriorityId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'low-priority-component',
        async () => {
          executionOrder.push('low');
          await new Promise(resolve => setTimeout(resolve, 100));
        },
        { priority: CleanupPriority.LOW }
      );

      const highPriorityId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'high-priority-component',
        async () => {
          executionOrder.push('high');
          await new Promise(resolve => setTimeout(resolve, 100));
        },
        { priority: CleanupPriority.HIGH }
      );

      const normalPriorityId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'normal-priority-component',
        async () => {
          executionOrder.push('normal');
          await new Promise(resolve => setTimeout(resolve, 100));
        },
        { priority: CleanupPriority.NORMAL }
      );

      // Wait for all operations to complete
      await new Promise(resolve => setTimeout(resolve, 3000));

      // High priority should execute first
      expect(executionOrder[0]).toBe('high');
      expect(executionOrder).toContain('normal');
      expect(executionOrder).toContain('low');
    });

    it('should cancel queued operations', async () => {
      let executed = false;
      const operation = async () => {
        executed = true;
      };

      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'test-component',
        operation
      );

      expect(coordinator.getOperationStatus(operationId)).toBe(CleanupStatus.QUEUED);

      const cancelled = coordinator.cancelCleanup(operationId);
      expect(cancelled).toBe(true);
      expect(coordinator.getOperationStatus(operationId)).toBe(CleanupStatus.CANCELLED);

      // Wait to ensure it doesn't execute
      await new Promise(resolve => setTimeout(resolve, 2000));
      expect(executed).toBe(false);
    });
  });

  describe('Concurrent Operation Testing', () => {
    it('should handle multiple concurrent operations', async () => {
      const executionCount = { value: 0 };
      const maxConcurrent = { value: 0 };
      const currentConcurrent = { value: 0 };

      const createOperation = (id: string) => async () => {
        currentConcurrent.value++;
        maxConcurrent.value = Math.max(maxConcurrent.value, currentConcurrent.value);
        
        await new Promise(resolve => setTimeout(resolve, 500));
        
        executionCount.value++;
        currentConcurrent.value--;
      };

      // Schedule 6 operations (more than max concurrent of 3)
      const operationIds: string[] = [];
      for (let i = 0; i < 6; i++) {
        const operationId = coordinator.scheduleCleanup(
          CleanupOperationType.RESOURCE_CLEANUP,
          `test-component-${i}`,
          createOperation(`op-${i}`)
        );
        operationIds.push(operationId);
      }

      // Wait for all operations to complete
      await new Promise(resolve => setTimeout(resolve, 4000));

      // All operations should have executed
      expect(executionCount.value).toBe(6);
      
      // Should not exceed max concurrent operations
      expect(maxConcurrent.value).toBeLessThanOrEqual(3);

      // All operations should be completed
      operationIds.forEach(id => {
        expect(coordinator.getOperationStatus(id)).toBe(CleanupStatus.COMPLETED);
      });
    });

    it('should handle operation failures and retries', async () => {
      let attemptCount = 0;
      const operation = async () => {
        attemptCount++;
        if (attemptCount < 3) {
          throw new Error(`Attempt ${attemptCount} failed`);
        }
        // Succeed on third attempt
      };

      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'failing-component',
        operation,
        { maxRetries: 3 }
      );

      // Wait for retries to complete
      await new Promise(resolve => setTimeout(resolve, 5000));

      expect(attemptCount).toBe(3);
      expect(coordinator.getOperationStatus(operationId)).toBe(CleanupStatus.COMPLETED);
    });

    it('should handle permanent failures after max retries', async () => {
      let attemptCount = 0;
      const operation = async () => {
        attemptCount++;
        throw new Error(`Attempt ${attemptCount} failed`);
      };

      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'always-failing-component',
        operation,
        { maxRetries: 2 }
      );

      // Wait for retries to complete
      await new Promise(resolve => setTimeout(resolve, 5000));

      expect(attemptCount).toBe(2); // Initial attempt + 1 retry (maxRetries = 2 means 2 total attempts)
      expect(coordinator.getOperationStatus(operationId)).toBe(CleanupStatus.FAILED);
    });
  });

  describe('Conflict Prevention Validation', () => {
    it('should detect and prevent conflicting operations', async () => {
      const executionOrder: string[] = [];

      // Schedule a shutdown operation (conflicts with everything)
      const shutdownId = coordinator.scheduleCleanup(
        CleanupOperationType.SHUTDOWN_CLEANUP,
        'shutdown-component',
        async () => {
          executionOrder.push('shutdown');
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      );

      // Schedule a conflicting timer cleanup
      const timerId = coordinator.scheduleCleanup(
        CleanupOperationType.TIMER_CLEANUP,
        'timer-component',
        async () => {
          executionOrder.push('timer');
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      );

      // Wait for operations to complete
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Shutdown should complete first, then timer
      expect(executionOrder).toEqual(['shutdown', 'timer']);
      expect(coordinator.getOperationStatus(shutdownId)).toBe(CleanupStatus.COMPLETED);
      expect(coordinator.getOperationStatus(timerId)).toBe(CleanupStatus.COMPLETED);

      // Check that conflicts were detected
      const metrics = coordinator.getMetrics();
      expect(metrics.conflictsPrevented).toBeGreaterThan(0);
    });

    it('should handle component-level locking', async () => {
      const executionOrder: string[] = [];
      const componentId = 'shared-component';

      // Schedule first operation
      const firstId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        componentId,
        async () => {
          executionOrder.push('first');
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      );

      // Schedule second operation on same component
      const secondId = coordinator.scheduleCleanup(
        CleanupOperationType.TIMER_CLEANUP,
        componentId,
        async () => {
          executionOrder.push('second');
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      );

      // Wait for operations to complete
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Operations should execute sequentially, not concurrently
      expect(executionOrder).toEqual(['first', 'second']);
      expect(coordinator.getOperationStatus(firstId)).toBe(CleanupStatus.COMPLETED);
      expect(coordinator.getOperationStatus(secondId)).toBe(CleanupStatus.COMPLETED);
    });
  });

  describe('Dependency Management', () => {
    it('should handle operation dependencies correctly', async () => {
      const executionOrder: string[] = [];

      // Schedule dependent operation first
      const dependentId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'dependent-component',
        async () => {
          executionOrder.push('dependent');
          await new Promise(resolve => setTimeout(resolve, 100));
        },
        { dependencies: ['dependency-op'] }
      );

      // Schedule dependency operation
      const dependencyId = coordinator.scheduleCleanup(
        CleanupOperationType.TIMER_CLEANUP,
        'dependency-component',
        async () => {
          executionOrder.push('dependency');
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      );

      // Manually set the dependency ID (in real usage, this would be managed properly)
      const dependentOp = (coordinator as any)._operations.get(dependentId);
      if (dependentOp) {
        dependentOp.dependencies = [dependencyId];
      }

      // Wait for operations to complete
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Dependency should execute first
      expect(executionOrder).toEqual(['dependency', 'dependent']);
    });
  });

  describe('Metrics and Monitoring', () => {
    it('should track operation metrics accurately', async () => {
      const initialMetrics = coordinator.getMetrics();
      expect(initialMetrics.totalOperations).toBe(0);

      // Schedule multiple operations
      const operationIds: string[] = [];
      for (let i = 0; i < 5; i++) {
        const operationId = coordinator.scheduleCleanup(
          CleanupOperationType.RESOURCE_CLEANUP,
          `metrics-component-${i}`,
          async () => {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        );
        operationIds.push(operationId);
      }

      // Wait for operations to complete
      await new Promise(resolve => setTimeout(resolve, 3000));

      const finalMetrics = coordinator.getMetrics();
      expect(finalMetrics.totalOperations).toBe(5);
      expect(finalMetrics.completedOperations).toBe(5);
      expect(finalMetrics.queuedOperations).toBe(0);
      expect(finalMetrics.runningOperations).toBe(0);
      expect(finalMetrics.averageExecutionTime).toBeGreaterThan(0);
    });

    it('should track operation types and priorities', async () => {
      // Schedule operations of different types and priorities
      coordinator.scheduleCleanup(
        CleanupOperationType.TIMER_CLEANUP,
        'timer-component',
        async () => {},
        { priority: CleanupPriority.HIGH }
      );

      coordinator.scheduleCleanup(
        CleanupOperationType.EVENT_HANDLER_CLEANUP,
        'event-component',
        async () => {},
        { priority: CleanupPriority.LOW }
      );

      // Wait for operations to complete
      await new Promise(resolve => setTimeout(resolve, 2000));

      const metrics = coordinator.getMetrics();
      expect(metrics.operationsByType[CleanupOperationType.TIMER_CLEANUP]).toBeGreaterThan(0);
      expect(metrics.operationsByType[CleanupOperationType.EVENT_HANDLER_CLEANUP]).toBeGreaterThan(0);
      expect(metrics.operationsByPriority[CleanupPriority.HIGH]).toBeGreaterThan(0);
      expect(metrics.operationsByPriority[CleanupPriority.LOW]).toBeGreaterThan(0);
    });
  });

  describe('Force Cleanup', () => {
    it('should execute force cleanup immediately', async () => {
      let executed = false;
      const operation = async () => {
        executed = true;
      };

      // Force cleanup should complete quickly
      const startTime = Date.now();
      await coordinator.forceComponentCleanup(
        CleanupOperationType.SHUTDOWN_CLEANUP,
        'emergency-component',
        operation
      );
      const endTime = Date.now();

      expect(executed).toBe(true);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should handle force cleanup failures', async () => {
      const operation = async () => {
        throw new Error('Force cleanup failed');
      };

      await expect(
        coordinator.forceComponentCleanup(
          CleanupOperationType.SHUTDOWN_CLEANUP,
          'failing-emergency-component',
          operation
        )
      ).rejects.toThrow('Force cleanup failed');
    });
  });
});
