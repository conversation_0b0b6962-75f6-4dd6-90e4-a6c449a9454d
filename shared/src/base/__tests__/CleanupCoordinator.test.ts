/**
 * @file Cleanup Coordinator Test Suite
 * @component cleanup-coordinator-tests
 * @authority-level critical-memory-safety
 *
 * 🚨 PHASE 4: Cleanup Operation Coordination Tests
 *
 * This test suite validates the CleanupCoordinator functionality:
 * - Basic operation scheduling and management
 * - Operation status tracking
 * - Metrics collection
 * - Configuration and initialization
 *
 * Note: Simplified for Jest timer compatibility
 */

import {
  CleanupCoordinator,
  CleanupOperationType,
  CleanupPriority,
  CleanupStatus,
  getCleanupCoordinator,
  resetCleanupCoordinator
} from '../CleanupCoordinator';

// Mock console for testing
const mockConsole = {
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
};

// Configure Jest timeout for cleanup tests
jest.setTimeout(10000); // 10 second timeout

describe('CleanupCoordinator', () => {
  let coordinator: CleanupCoordinator;

  beforeEach(async () => {
    // Reset any existing coordinator
    resetCleanupCoordinator();

    // Reset console mocks
    Object.values(mockConsole).forEach(mock => mock.mockClear());

    // Override console methods for logging tests
    global.console = {
      ...global.console,
      ...mockConsole
    };

    // Create fresh coordinator instance with test configuration
    coordinator = getCleanupCoordinator({
      maxConcurrentOperations: 3,
      defaultTimeout: 1000,
      maxRetries: 2,
      conflictDetectionEnabled: true,
      metricsEnabled: true,
      cleanupIntervalMs: 100
    });

    await coordinator.initialize();
  });

  afterEach(async () => {
    if (coordinator) {
      await coordinator.shutdown();
    }
    resetCleanupCoordinator();
  });

  describe('Basic Operation Management', () => {
    it('should schedule cleanup operations', () => {
      const operation = async () => {};

      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'test-component',
        operation
      );

      expect(operationId).toBeDefined();
      expect(typeof operationId).toBe('string');
      expect(coordinator.getOperationStatus(operationId)).toBe(CleanupStatus.QUEUED);
    });

    it('should track operation status', () => {
      const operation = async () => {};

      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'test-component',
        operation
      );

      expect(coordinator.getOperationStatus(operationId)).toBe(CleanupStatus.QUEUED);

      // Test non-existent operation
      expect(coordinator.getOperationStatus('non-existent')).toBeUndefined();
    });

    it('should cancel queued operations', () => {
      const operation = async () => {};

      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'test-component',
        operation
      );

      expect(coordinator.getOperationStatus(operationId)).toBe(CleanupStatus.QUEUED);

      const cancelled = coordinator.cancelCleanup(operationId);
      expect(cancelled).toBe(true);
      expect(coordinator.getOperationStatus(operationId)).toBe(CleanupStatus.CANCELLED);

      // Test cancelling non-existent operation
      expect(coordinator.cancelCleanup('non-existent')).toBe(false);
    });

    it('should handle different operation types', () => {
      const operation = async () => {};

      const timerOpId = coordinator.scheduleCleanup(
        CleanupOperationType.TIMER_CLEANUP,
        'timer-component',
        operation
      );

      const eventOpId = coordinator.scheduleCleanup(
        CleanupOperationType.EVENT_HANDLER_CLEANUP,
        'event-component',
        operation
      );

      const bufferOpId = coordinator.scheduleCleanup(
        CleanupOperationType.BUFFER_CLEANUP,
        'buffer-component',
        operation
      );

      expect(coordinator.getOperationStatus(timerOpId)).toBe(CleanupStatus.QUEUED);
      expect(coordinator.getOperationStatus(eventOpId)).toBe(CleanupStatus.QUEUED);
      expect(coordinator.getOperationStatus(bufferOpId)).toBe(CleanupStatus.QUEUED);
    });

    it('should handle different priority levels', () => {
      const operation = async () => {};

      const lowPriorityId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'low-component',
        operation,
        { priority: CleanupPriority.LOW }
      );

      const highPriorityId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'high-component',
        operation,
        { priority: CleanupPriority.HIGH }
      );

      const criticalPriorityId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'critical-component',
        operation,
        { priority: CleanupPriority.CRITICAL }
      );

      expect(coordinator.getOperationStatus(lowPriorityId)).toBe(CleanupStatus.QUEUED);
      expect(coordinator.getOperationStatus(highPriorityId)).toBe(CleanupStatus.QUEUED);
      expect(coordinator.getOperationStatus(criticalPriorityId)).toBe(CleanupStatus.QUEUED);
    });
  });

  describe('Configuration and Options', () => {
    it('should handle operation options', () => {
      const operation = async () => {};

      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'test-component',
        operation,
        {
          priority: CleanupPriority.HIGH,
          timeout: 5000,
          maxRetries: 3,
          metadata: { testData: 'value' }
        }
      );

      expect(operationId).toBeDefined();
      expect(coordinator.getOperationStatus(operationId)).toBe(CleanupStatus.QUEUED);
    });

    it('should handle dependencies', () => {
      const operation = async () => {};

      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'dependent-component',
        operation,
        {
          dependencies: ['dependency-1', 'dependency-2']
        }
      );

      expect(operationId).toBeDefined();
      expect(coordinator.getOperationStatus(operationId)).toBe(CleanupStatus.QUEUED);
    });
  });

  describe('Metrics and Monitoring', () => {
    it('should track operation metrics accurately', async () => {
      const initialMetrics = coordinator.getMetrics();
      expect(initialMetrics.totalOperations).toBe(0);

      // Schedule multiple operations
      for (let i = 0; i < 3; i++) {
        coordinator.scheduleCleanup(
          CleanupOperationType.RESOURCE_CLEANUP,
          `metrics-component-${i}`,
          async () => {}
        );
      }

      // Advance timers to trigger operation processing
      jest.advanceTimersByTime(1100);
      await Promise.resolve();
      await Promise.resolve();

      const finalMetrics = coordinator.getMetrics();
      expect(finalMetrics.totalOperations).toBe(3);
      expect(finalMetrics.completedOperations).toBe(3);
    });

    it('should track operation types and priorities', async () => {
      // Schedule operations of different types and priorities
      coordinator.scheduleCleanup(
        CleanupOperationType.TIMER_CLEANUP,
        'timer-component',
        async () => {},
        { priority: CleanupPriority.HIGH }
      );

      coordinator.scheduleCleanup(
        CleanupOperationType.EVENT_HANDLER_CLEANUP,
        'event-component',
        async () => {},
        { priority: CleanupPriority.LOW }
      );

      // Advance timers to trigger operation processing
      jest.advanceTimersByTime(1100);
      await Promise.resolve();
      await Promise.resolve();

      const metrics = coordinator.getMetrics();
      expect(metrics.totalOperations).toBe(2);
      expect(metrics.completedOperations).toBe(2);
    });
  });

  describe('Basic Functionality', () => {
    it('should provide basic cleanup coordination', async () => {
      let executed = false;
      const operation = async () => {
        executed = true;
      };

      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'basic-component',
        operation
      );

      expect(operationId).toBeDefined();
      expect(coordinator.getOperationStatus(operationId)).toBe(CleanupStatus.QUEUED);

      // Advance timers to trigger operation processing
      jest.advanceTimersByTime(1100);
      await Promise.resolve();

      expect(executed).toBe(true);
      expect(coordinator.getOperationStatus(operationId)).toBe(CleanupStatus.COMPLETED);
    });
  });
});
