/**
 * @file Jest Configuration
 * @filepath jest.config.js
 * @description Jest configuration for OA Framework testing
 * @created 2025-06-26 00:26:23 +03
 * @authority President & CEO, E.Z. Consultancy
 */

module.exports = {
  // Test environment
  testEnvironment: 'node',
  testEnvironmentOptions: {
    NODE_ENV: 'test',
    TEST_TYPE: process.env.TEST_TYPE || 'unit'
  },

  // File extensions to handle
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

  // Transform TypeScript files
  transform: {
    '^.+\\.ts$': ['ts-jest', {
      tsconfig: 'tsconfig.test.json'
    }],
  },

  // Test file patterns
  testMatch: [
    '**/__tests__/**/*.test.ts',
    '**/?(*.)+(spec|test).ts'
  ],

  // Setup files
  setupFilesAfterEnv: [
    '<rootDir>/jest.setup.js',
    '<rootDir>/tests/setup/testSetup.ts'
  ],

  // CRITICAL FIX: Disable coverage collection to prevent massive memory leaks
  // Coverage instrumentation holds references to all modules causing memory accumulation
  collectCoverage: false,
  // coverageDirectory: 'coverage',
  // coverageReporters: ['text', 'lcov', 'html'],
  // collectCoverageFrom: [
  //   'server/**/*.ts',
  //   'shared/**/*.ts',
  //   '!**/*.d.ts',
  //   '!**/__tests__/**',
  //   '!**/node_modules/**',
  // ],

  // Module path mapping
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
  },

  // Ignore patterns
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/coverage/',
  ],

  // TypeScript configuration
  preset: 'ts-jest',

  // Global setup
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json',
    },
  },

  // Global setup and teardown for test isolation
  globalSetup: '<rootDir>/tests/setup/globalSetup.ts',
  globalTeardown: '<rootDir>/tests/setup/globalTeardown.ts',

  // Custom test sequencer for proper test order
  testSequencer: '<rootDir>/tests/setup/testSequencer.js',

  // Test timeout - increased for cleanup
  testTimeout: 30000,

  // Verbose output
  verbose: true,

  // Clear mocks between tests
  clearMocks: true,

  // CRITICAL FIX: Disable resetModules to prevent memory accumulation
  // resetModules: true, // DISABLED - causes massive memory leaks with fake timers

  // Force sequential test execution for memory isolation
  maxWorkers: 1,

  // Memory management - Enhanced for leak detection
  logHeapUsage: true,
  detectOpenHandles: true,
  forceExit: true,

  // CRITICAL FIX: Add memory leak detection
  detectLeaks: true,

  // Cache directory
  cacheDirectory: '<rootDir>/.jest-cache',

  // Coverage threshold
  coverageThreshold: {
    global: {
      branches: 20,
      functions: 20,
      lines: 20,
      statements: 20,
    },
  },
};
