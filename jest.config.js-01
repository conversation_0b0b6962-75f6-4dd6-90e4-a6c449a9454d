/**
 * @file Jest Configuration - Enhanced for OrchestrationCoordinator Testing
 * @filepath jest.config.js
 * @description Enhanced Jest configuration for OA Framework enterprise testing
 * @created 2025-06-26 00:26:23 +03
 * @modified 2025-07-09 (Enhanced for OrchestrationCoordinator)
 * @authority President & CEO, E.Z. Consultancy
 * 
 * ENHANCEMENTS:
 * - Added path mapping for complex import structures
 * - Enhanced coverage thresholds for enterprise standards
 * - Specific configuration for OrchestrationCoordinator testing
 * - Performance testing optimizations
 * - Memory boundary testing support
 */

module.exports = {
  // Test environment
  testEnvironment: 'node',

  // File extensions to handle
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

  // Transform TypeScript files with enhanced configuration
  transform: {
    '^.+\\.ts$': ['ts-jest', {
      tsconfig: {
        target: 'es2020',
        module: 'commonjs',
        lib: ['es2020'],
        allowJs: true,
        checkJs: false,
        declaration: false,
        declarationMap: false,
        sourceMap: true,
        outDir: './dist',
        removeComments: true,
        strict: true,
        noImplicitAny: true,
        strictNullChecks: true,
        strictFunctionTypes: true,
        noImplicitReturns: true,
        noFallthroughCasesInSwitch: true,
        moduleResolution: 'node',
        baseUrl: './',
        esModuleInterop: true,
        experimentalDecorators: true,
        emitDecoratorMetadata: true,
        skipLibCheck: true,
        forceConsistentCasingInFileNames: true,
        resolveJsonModule: true
      }
    }],
  },

  // Test file patterns - Enhanced for specific test structure
  testMatch: [
    '**/__tests__/**/*.test.ts',
    '**/?(*.)+(spec|test).ts',
    '**/server/src/platform/tracking/advanced-data/__tests__/OrchestrationCoordinator.test.ts'
  ],

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],

  // Coverage configuration - Enhanced for enterprise standards
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json-summary'],
  collectCoverageFrom: [
    'server/**/*.ts',
    'shared/**/*.ts',
    '!**/*.d.ts',
    '!**/__tests__/**',
    '!**/node_modules/**',
    '!**/dist/**',
    '!**/build/**',
    '!**/coverage/**',
    // Specific inclusion for OrchestrationCoordinator
    'server/src/platform/tracking/advanced-data/OrchestrationCoordinator.ts',
    'server/src/platform/tracking/core-data/base/BaseTrackingService.ts'
  ],

  // Enhanced module path mapping for complex import structures
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
    '^@server/(.*)$': '<rootDir>/server/src/$1',
    '^@shared/(.*)$': '<rootDir>/shared/src/$1',
    '^@tracking/(.*)$': '<rootDir>/server/src/platform/tracking/$1',
    '^@governance/(.*)$': '<rootDir>/server/src/platform/governance/$1',
    // Specific mappings for OrchestrationCoordinator dependencies
    '^@/server/src/platform/tracking/core-data/base/BaseTrackingService$': '<rootDir>/server/src/platform/tracking/core-data/base/BaseTrackingService.ts',
    '^@/shared/src/types/platform/tracking/tracking-types$': '<rootDir>/shared/src/types/platform/tracking/tracking-types.ts',
    '^@/shared/src/constants/platform/tracking/tracking-constants$': '<rootDir>/shared/src/constants/platform/tracking/tracking-constants.ts'
  },

  // Root directories
  roots: [
    '<rootDir>/server',
    '<rootDir>/shared',
    '<rootDir>/tests'
  ],

  // Ignore patterns
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/coverage/',
    '/.git/',
    '/.vscode/',
    '/docs/'
  ],

  // TypeScript configuration
  preset: 'ts-jest',

  // Global setup with enhanced configuration
  globals: {
    'ts-jest': {
      tsconfig: {
        target: 'es2020',
        module: 'commonjs',
        esModuleInterop: true,
        allowSyntheticDefaultImports: true,
        experimentalDecorators: true,
        emitDecoratorMetadata: true
      },
      isolatedModules: true,
      useESM: false
    }
  },

  // Test timeout - Enhanced for performance testing
  testTimeout: 60000,

  // Verbose output with enhanced reporting
  verbose: true,
  detectOpenHandles: true,
  detectLeaks: true,

  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,

  // Reset modules between tests
  resetModules: true,

  // Maximum worker processes - Optimized for CI/CD
  maxWorkers: process.env.CI ? 2 : '50%',

  // Cache directory
  cacheDirectory: '<rootDir>/.jest-cache',

  // Enhanced coverage threshold for enterprise standards
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90,
    },
    // Specific thresholds for critical components
    'server/src/platform/tracking/advanced-data/OrchestrationCoordinator.ts': {
      branches: 95,
      functions: 100,
      lines: 95,
      statements: 95,
    },
    'server/src/platform/tracking/core-data/base/BaseTrackingService.ts': {
      branches: 80,
      functions: 85,
      lines: 85,
      statements: 85,
    }
  },

  // Error reporting
  errorOnDeprecated: true,
  
  // Performance and memory optimizations
  workerIdleMemoryLimit: '1GB',
  
  // Test result processor for enhanced reporting
  testResultsProcessor: undefined,

  // Watch plugins for development
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname'
  ],

  // Reporter configuration
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: 'coverage',
      outputName: 'junit.xml',
      ancestorSeparator: ' › ',
      uniqueOutputName: 'false',
      suiteNameTemplate: '{filepath}',
      classNameTemplate: '{classname}',
      titleTemplate: '{title}'
    }]
  ],

  // Transform ignore patterns
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$))'
  ],

  // ESM support
  extensionsToTreatAsEsm: ['.ts'],

  // Bail configuration for CI/CD
  bail: process.env.CI ? 1 : false,

  // Force exit in CI environments
  forceExit: process.env.CI ? true : false,

  // Test name pattern for specific test execution
  testNamePattern: process.env.TEST_NAME_PATTERN || undefined,

  // Collection of unused exports (for cleanup)
  collectCoverageOnlyFrom: undefined,

  // Custom environment options
  testEnvironmentOptions: {
    node: {
      experimental: true
    }
  }
};