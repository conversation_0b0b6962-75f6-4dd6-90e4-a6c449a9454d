oa-prod$ npm test -- --testPathPattern="EventHandlerRegistry.integration.test.ts" --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=EventHandlerRegistry.integration.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/EventHandlerRegistry.integration.test.ts (63.351 s, 365 MB heap size)
  EventHandlerRegistry Integration Tests
    M0 Testing Compatibility
      ✕ should handle extensive event subscription/unsubscription cycles without memory leaks (13 ms)
      ✓ should provide deterministic cleanup on client disconnection (5 ms)
      ✕ should detect and report orphaned handlers (60007 ms)
    Performance and Scalability
      ✓ should handle high-volume handler registration efficiently (19 ms)
      ✓ should maintain consistent performance under concurrent access (21 ms)
    Error Handling and Recovery
      ✓ should handle invalid handler operations gracefully (3 ms)
      ✓ should enforce handler limits correctly (18 ms)
    Memory Safety Validation
      ✓ should not retain references after handler cleanup (3 ms)

  ● EventHandlerRegistry Integration Tests › M0 Testing Compatibility › should handle extensive event subscription/unsubscription cycles without memory leaks

    Client test-client-m0 has exceeded maximum handler limit

      286 |     if (clientHandlerCount >= this._config.maxHandlersPerClient) {
      287 |       this.logWarning('Client handler limit exceeded', { clientId, limit: this._config.maxHandlersPerClient });
    > 288 |       throw new Error(`Client ${clientId} has exceeded maximum handler limit`);
          |             ^
      289 |     }
      290 |
      291 |     // Check global handler limits

      at EventHandlerRegistry.registerHandler (shared/src/base/EventHandlerRegistry.ts:288:13)
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistry.integration.test.ts:80:38)

  ● EventHandlerRegistry Integration Tests › M0 Testing Compatibility › should detect and report orphaned handlers

    thrown: "Exceeded timeout of 60000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      141 |     });
      142 |
    > 143 |     it('should detect and report orphaned handlers', async () => {
          |     ^
      144 |       const clientId = 'test-client-orphan';
      145 |       const callback = jest.fn();
      146 |

      at shared/src/base/__tests__/EventHandlerRegistry.integration.test.ts:143:5
      at shared/src/base/__tests__/EventHandlerRegistry.integration.test.ts:69:3
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistry.integration.test.ts:29:1)

Test Suites: 1 failed, 1 total
Tests:       2 failed, 6 passed, 8 total
Snapshots:   0 total
Time:        63.611 s
Ran all test suites matching /EventHandlerRegistry.integration.test.ts/i.
