oa-prod$ npm test -- --testPathPattern="MemorySafeSystem.integration.test.ts" --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=MemorySafeSystem.integration.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/MemorySafeSystem.integration.test.ts (108.5 s, 367 MB heap size)
  Memory Safe System Integration Tests
    Cross-Component Integration
      ✓ should initialize all components successfully (10 ms)
      ✕ should coordinate operations across all components (15010 ms)
      ✕ should handle cross-component dependencies (15010 ms)
    System-Wide Memory Leak Prevention
      ✕ should prevent memory leaks during normal operations (12 ms)
      ✓ should detect and handle memory leaks (10 ms)
    Coordinated Shutdown Procedures
      ✓ should execute coordinated shutdown across all components (9 ms)
      ✓ should handle shutdown timeout gracefully (9 ms)
    Error Conditions and Recovery
      ✕ should handle component failures gracefully (15009 ms)
      ✓ should recover from emergency cleanup scenarios (9 ms)
    Performance Impact Validation
      ✕ should maintain low performance overhead (15008 ms)
      ✕ should scale efficiently with increased load (15009 ms)
    Real-World Usage Patterns
      ✕ should handle web server simulation (15007 ms)
      ✕ should handle high-frequency operations (15008 ms)

  ● Memory Safe System Integration Tests › Cross-Component Integration › should coordinate operations across all components

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      125 |     });
      126 |
    > 127 |     it('should coordinate operations across all components', async () => {
          |     ^
      128 |       const eventRegistry = getEventHandlerRegistry();
      129 |       const timerCoordinator = getTimerCoordinator();
      130 |       const cleanupCoordinator = getCleanupCoordinator();

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:127:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:112:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › Cross-Component Integration › should handle cross-component dependencies

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      172 |     });
      173 |
    > 174 |     it('should handle cross-component dependencies', async () => {
          |     ^
      175 |       const cleanupCoordinator = getCleanupCoordinator();
      176 |       const executionOrder: string[] = [];
      177 |

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:174:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:112:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › System-Wide Memory Leak Prevention › should prevent memory leaks during normal operations

    expect(received).toBeLessThan(expected)

    Expected: < 1048576
    Received:   1048576

      241 |       // Memory usage should not have increased significantly
      242 |       const memoryIncrease = finalMetrics.totalMemoryUsageBytes - initialMetrics.totalMemoryUsageBytes;
    > 243 |       expect(memoryIncrease).toBeLessThan(1024 * 1024); // Less than 1MB increase
          |                              ^
      244 |
      245 |       // No handlers should remain
      246 |       expect(finalMetrics.eventHandlers.totalHandlers).toBe(0);

      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:243:30)

  ● Memory Safe System Integration Tests › Error Conditions and Recovery › should handle component failures gracefully

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      329 |
      330 |   describe('Error Conditions and Recovery', () => {
    > 331 |     it('should handle component failures gracefully', async () => {
          |     ^
      332 |       const cleanupCoordinator = getCleanupCoordinator();
      333 |
      334 |       // Schedule operations that will fail

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:331:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:330:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › Performance Impact Validation › should maintain low performance overhead

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      397 |
      398 |   describe('Performance Impact Validation', () => {
    > 399 |     it('should maintain low performance overhead', async () => {
          |     ^
      400 |       const performanceStart = process.hrtime.bigint();
      401 |
      402 |       // Perform standard operations (simplified for testing)

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:399:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:398:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › Performance Impact Validation › should scale efficiently with increased load

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      439 |     });
      440 |
    > 441 |     it('should scale efficiently with increased load', async () => {
          |     ^
      442 |       const loadSizes = [5, 10, 15]; // Reduced load sizes
      443 |       const executionTimes: number[] = [];
      444 |

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:441:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:398:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › Real-World Usage Patterns › should handle web server simulation

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      487 |
      488 |   describe('Real-World Usage Patterns', () => {
    > 489 |     it('should handle web server simulation', async () => {
          |     ^
      490 |       const eventRegistry = getEventHandlerRegistry();
      491 |       const cleanupCoordinator = getCleanupCoordinator();
      492 |

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:489:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:488:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › Real-World Usage Patterns › should handle high-frequency operations

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      534 |     });
      535 |
    > 536 |     it('should handle high-frequency operations', async () => {
          |     ^
      537 |       const cleanupCoordinator = getCleanupCoordinator();
      538 |       let operationsCompleted = 0;
      539 |

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:536:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:488:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

Test Suites: 1 failed, 1 total
Tests:       8 failed, 5 passed, 13 total
Snapshots:   0 total
Time:        108.758 s
Ran all test suites matching /MemorySafeSystem.integration.test.ts/i.
