oa-prod$ npm test -- --testPathPattern="MemorySafeSystem.integration.test.ts" --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=MemorySafeSystem.integration.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/MemorySafeSystem.integration.test.ts (108.256 s, 369 MB heap size)
  Memory Safe System Integration Tests
    Cross-Component Integration
      ✓ should initialize all components successfully (10 ms)
      ✕ should coordinate operations across all components (15013 ms)
      ✕ should handle cross-component dependencies (15009 ms)
    System-Wide Memory Leak Prevention
      ✓ should prevent memory leaks during normal operations (12 ms)
      ✕ should detect and handle memory leaks (10 ms)
    Coordinated Shutdown Procedures
      ✓ should execute coordinated shutdown across all components (9 ms)
      ✓ should handle shutdown timeout gracefully (9 ms)
    Error Conditions and Recovery
      ✕ should handle component failures gracefully (15012 ms)
      ✓ should recover from emergency cleanup scenarios (10 ms)
    Performance Impact Validation
      ✕ should maintain low performance overhead (15010 ms)
      ✕ should scale efficiently with increased load (15010 ms)
    Real-World Usage Patterns
      ✕ should handle web server simulation (15006 ms)
      ✕ should handle high-frequency operations (15010 ms)

  ● Memory Safe System Integration Tests › Cross-Component Integration › should coordinate operations across all components

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      125 |     });
      126 |
    > 127 |     it('should coordinate operations across all components', async () => {
          |     ^
      128 |       const eventRegistry = getEventHandlerRegistry();
      129 |       const timerCoordinator = getTimerCoordinator();
      130 |       const cleanupCoordinator = getCleanupCoordinator();

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:127:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:112:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › Cross-Component Integration › should handle cross-component dependencies

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      159 |     });
      160 |
    > 161 |     it('should handle cross-component dependencies', async () => {
          |     ^
      162 |       const cleanupCoordinator = getCleanupCoordinator();
      163 |       const executionOrder: string[] = [];
      164 |

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:161:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:112:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › System-Wide Memory Leak Prevention › should detect and handle memory leaks

    expect(received).toBeLessThan(expected)

    Expected: < 100
    Received:   100

      248 |
      249 |       // System health should be impacted but still functional
    > 250 |       expect(metrics.systemHealthScore).toBeLessThan(100);
          |                                         ^
      251 |       expect(metrics.systemHealthScore).toBeGreaterThan(50);
      252 |     });
      253 |   });

      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:250:41)

  ● Memory Safe System Integration Tests › Error Conditions and Recovery › should handle component failures gracefully

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      309 |
      310 |   describe('Error Conditions and Recovery', () => {
    > 311 |     it('should handle component failures gracefully', async () => {
          |     ^
      312 |       const cleanupCoordinator = getCleanupCoordinator();
      313 |
      314 |       // Schedule operations that will fail

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:311:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:310:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › Performance Impact Validation › should maintain low performance overhead

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      370 |
      371 |   describe('Performance Impact Validation', () => {
    > 372 |     it('should maintain low performance overhead', async () => {
          |     ^
      373 |       const performanceStart = process.hrtime.bigint();
      374 |
      375 |       // Perform standard operations (simplified for testing)

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:372:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:371:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › Performance Impact Validation › should scale efficiently with increased load

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      405 |     });
      406 |
    > 407 |     it('should scale efficiently with increased load', async () => {
          |     ^
      408 |       const loadSizes = [5, 10, 15]; // Reduced load sizes
      409 |       const executionTimes: number[] = [];
      410 |

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:407:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:371:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › Real-World Usage Patterns › should handle web server simulation

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      446 |
      447 |   describe('Real-World Usage Patterns', () => {
    > 448 |     it('should handle web server simulation', async () => {
          |     ^
      449 |       const eventRegistry = getEventHandlerRegistry();
      450 |       const cleanupCoordinator = getCleanupCoordinator();
      451 |

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:448:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:447:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › Real-World Usage Patterns › should handle high-frequency operations

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      486 |     });
      487 |
    > 488 |     it('should handle high-frequency operations', async () => {
          |     ^
      489 |       const cleanupCoordinator = getCleanupCoordinator();
      490 |       let operationsCompleted = 0;
      491 |

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:488:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:447:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

Test Suites: 1 failed, 1 total
Tests:       8 failed, 5 passed, 13 total
Snapshots:   0 total
Time:        108.518 s
Ran all test suites matching /MemorySafeSystem.integration.test.ts/i.
