oa-prod$ npm test -- --testPathPattern="MemorySafeSystem.integration.test.ts" --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=MemorySafeSystem.integration.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/MemorySafeSystem.integration.test.ts (78.529 s, 370 MB heap size)
  Memory Safe System Integration Tests
    Cross-Component Integration
      ✓ should initialize all components successfully (11 ms)
      ✕ should coordinate operations across all components (15011 ms)
      ✕ should handle cross-component dependencies (15012 ms)
    System-Wide Memory Leak Prevention
      ✓ should prevent memory leaks during normal operations (11 ms)
      ✕ should detect and handle memory leaks (9 ms)
    Coordinated Shutdown Procedures
      ✓ should execute coordinated shutdown across all components (9 ms)
      ✕ should handle shutdown timeout gracefully (10 ms)
    Error Conditions and Recovery
      ✕ should handle component failures gracefully (15007 ms)
      ✕ should recover from emergency cleanup scenarios (14 ms)
    Performance Impact Validation
      ✕ should maintain low performance overhead (8 ms)
      ✕ should scale efficiently with increased load (15007 ms)
    Real-World Usage Patterns
      ✕ should handle web server simulation (7 ms)
      ✕ should handle high-frequency operations (15008 ms)

  ● Memory Safe System Integration Tests › Cross-Component Integration › should coordinate operations across all components

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      125 |     });
      126 |
    > 127 |     it('should coordinate operations across all components', async () => {
          |     ^
      128 |       const eventRegistry = getEventHandlerRegistry();
      129 |       const timerCoordinator = getTimerCoordinator();
      130 |       const cleanupCoordinator = getCleanupCoordinator();

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:127:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:112:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › Cross-Component Integration › should handle cross-component dependencies

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      173 |     });
      174 |
    > 175 |     it('should handle cross-component dependencies', async () => {
          |     ^
      176 |       const cleanupCoordinator = getCleanupCoordinator();
      177 |       const executionOrder: string[] = [];
      178 |

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:175:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:112:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › System-Wide Memory Leak Prevention › should detect and handle memory leaks

    Client memory-leak-client has exceeded maximum handler limit

      286 |     if (clientHandlerCount >= this._config.maxHandlersPerClient) {
      287 |       this.logWarning('Client handler limit exceeded', { clientId, limit: this._config.maxHandlersPerClient });
    > 288 |       throw new Error(`Client ${clientId} has exceeded maximum handler limit`);
          |             ^
      289 |     }
      290 |
      291 |     // Check global handler limits

      at EventHandlerRegistry.registerHandler (shared/src/base/EventHandlerRegistry.ts:288:13)
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:252:23)

  ● Memory Safe System Integration Tests › Coordinated Shutdown Procedures › should handle shutdown timeout gracefully

    expect(jest.fn()).toHaveBeenCalledWith(...expected)

    Expected: StringContaining "Timeout waiting for operations to complete", Any<String>
    Received: "[WARNING] MemorySafetyManager: Shutdown already in progress", ""

    Number of calls: 1

      326 |
      327 |       // Should log warnings about timeout
    > 328 |       expect(mockConsole.warn).toHaveBeenCalledWith(
          |                                ^
      329 |         expect.stringContaining('Timeout waiting for operations to complete'),
      330 |         expect.any(String)
      331 |       );

      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:328:32)

  ● Memory Safe System Integration Tests › Error Conditions and Recovery › should handle component failures gracefully

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      334 |
      335 |   describe('Error Conditions and Recovery', () => {
    > 336 |     it('should handle component failures gracefully', async () => {
          |     ^
      337 |       const cleanupCoordinator = getCleanupCoordinator();
      338 |
      339 |       // Schedule operations that will fail

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:336:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:335:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › Error Conditions and Recovery › should recover from emergency cleanup scenarios

    Global timer limit exceeded: 50/50

      468 |     // Enforce global timer limits (check before base class limits)
      469 |     if (this._timerRegistry.size >= this._config.maxGlobalTimers) {
    > 470 |       throw new Error(`Global timer limit exceeded: ${this._timerRegistry.size}/${this._config.maxGlobalTimers}`);
          |             ^
      471 |     }
      472 |
      473 |     // Check base class resource capacity to ensure we don't hit base class limits

      at TimerCoordinationService.createCoordinatedInterval (shared/src/base/TimerCoordinationService.ts:470:13)
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:381:26)

  ● Memory Safe System Integration Tests › Performance Impact Validation › should maintain low performance overhead

    Global timer limit exceeded: 50/50

      468 |     // Enforce global timer limits (check before base class limits)
      469 |     if (this._timerRegistry.size >= this._config.maxGlobalTimers) {
    > 470 |       throw new Error(`Global timer limit exceeded: ${this._timerRegistry.size}/${this._config.maxGlobalTimers}`);
          |             ^
      471 |     }
      472 |
      473 |     // Check base class resource capacity to ensure we don't hit base class limits

      at TimerCoordinationService.createCoordinatedInterval (shared/src/base/TimerCoordinationService.ts:470:13)
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:416:26)

  ● Memory Safe System Integration Tests › Performance Impact Validation › should scale efficiently with increased load

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      439 |     });
      440 |
    > 441 |     it('should scale efficiently with increased load', async () => {
          |     ^
      442 |       const loadSizes = [10, 50, 100];
      443 |       const executionTimes: number[] = [];
      444 |

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:441:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:402:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

  ● Memory Safe System Integration Tests › Real-World Usage Patterns › should handle web server simulation

    Global timer limit exceeded: 50/50

      468 |     // Enforce global timer limits (check before base class limits)
      469 |     if (this._timerRegistry.size >= this._config.maxGlobalTimers) {
    > 470 |       throw new Error(`Global timer limit exceeded: ${this._timerRegistry.size}/${this._config.maxGlobalTimers}`);
          |             ^
      471 |     }
      472 |
      473 |     // Check base class resource capacity to ensure we don't hit base class limits

      at TimerCoordinationService.createCoordinatedInterval (shared/src/base/TimerCoordinationService.ts:470:13)
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:501:26)

  ● Memory Safe System Integration Tests › Real-World Usage Patterns › should handle high-frequency operations

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      541 |     });
      542 |
    > 543 |     it('should handle high-frequency operations', async () => {
          |     ^
      544 |       const cleanupCoordinator = getCleanupCoordinator();
      545 |       let operationsCompleted = 0;
      546 |

      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:543:5
      at shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:479:3
      at Object.<anonymous> (shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:45:1)

Test Suites: 1 failed, 1 total
Tests:       10 failed, 3 passed, 13 total
Snapshots:   0 total
Time:        78.797 s
Ran all test suites matching /MemorySafeSystem.integration.test.ts/i.
