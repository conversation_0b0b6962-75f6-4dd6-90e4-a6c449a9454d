oa-prod$ npm test -- --testPathPattern="MemorySafeSystem.integration.test.ts" --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=MemorySafeSystem.integration.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/MemorySafeSystem.integration.test.ts
  ● Test suite failed to run

    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:102:31 - error TS2445: Property 'initialize' is protected and only accessible within class 'MemorySafeResourceManager' and its subclasses.

    102     await memorySafetyManager.initialize();
                                      ~~~~~~~~~~
    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:139:24 - error TS2551: Property 'createCoordinatedTimer' does not exist on type 'TimerCoordinationService'. Did you mean 'removeCoordinatedTimer'?

    139       timerCoordinator.createCoordinatedTimer(
                               ~~~~~~~~~~~~~~~~~~~~~~

      shared/src/base/TimerCoordinationService.ts:559:10
        559   public removeCoordinatedTimer(compositeId: string): void {
                     ~~~~~~~~~~~~~~~~~~~~~~
        'removeCoordinatedTimer' is declared here.
    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:166:28 - error TS2551: Property 'getHandlerCount' does not exist on type 'EventHandlerRegistry'. Did you mean 'getHandler'?

    166       expect(eventRegistry.getHandlerCount(clientId)).toBe(2);
                                   ~~~~~~~~~~~~~~~

      shared/src/base/EventHandlerRegistry.ts:402:10
        402   public getHandler(handlerId: string): IRegisteredHandler | undefined {
                     ~~~~~~~~~~
        'getHandler' is declared here.
    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:217:51 - error TS2554: Expected 1 arguments, but got 2.

    217         eventRegistry.unregisterHandler(clientId, handlerId);
                                                          ~~~~~~~~~
    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:220:42 - error TS2551: Property 'createCoordinatedTimer' does not exist on type 'TimerCoordinationService'. Did you mean 'removeCoordinatedTimer'?

    220         const timerId = timerCoordinator.createCoordinatedTimer(
                                                 ~~~~~~~~~~~~~~~~~~~~~~

      shared/src/base/TimerCoordinationService.ts:559:10
        559   public removeCoordinatedTimer(compositeId: string): void {
                     ~~~~~~~~~~~~~~~~~~~~~~
        'removeCoordinatedTimer' is declared here.
    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:225:26 - error TS2339: Property 'clearCoordinatedTimer' does not exist on type 'TimerCoordinationService'.

    225         timerCoordinator.clearCoordinatedTimer(timerId);
                                 ~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:229:33 - error TS2445: Property 'forceCleanup' is protected and only accessible within class 'MemorySafeResourceManager' and its subclasses.

    229       await memorySafetyManager.forceCleanup();
                                        ~~~~~~~~~~~~
    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:254:27 - error TS2339: Property 'updateMetrics' does not exist on type 'MemorySafetyManager'.

    254       memorySafetyManager.updateMetrics();
                                  ~~~~~~~~~~~~~
    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:276:24 - error TS2551: Property 'createCoordinatedTimer' does not exist on type 'TimerCoordinationService'. Did you mean 'removeCoordinatedTimer'?

    276       timerCoordinator.createCoordinatedTimer(() => {}, 5000, 'shutdown-timer');
                               ~~~~~~~~~~~~~~~~~~~~~~

      shared/src/base/TimerCoordinationService.ts:559:10
        559   public removeCoordinatedTimer(compositeId: string): void {
                     ~~~~~~~~~~~~~~~~~~~~~~
        'removeCoordinatedTimer' is declared here.
    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:379:26 - error TS2551: Property 'createCoordinatedTimer' does not exist on type 'TimerCoordinationService'. Did you mean 'removeCoordinatedTimer'?

    379         timerCoordinator.createCoordinatedTimer(() => {}, 1000, `emergency-timer-${i}`);
                                 ~~~~~~~~~~~~~~~~~~~~~~

      shared/src/base/TimerCoordinationService.ts:559:10
        559   public removeCoordinatedTimer(compositeId: string): void {
                     ~~~~~~~~~~~~~~~~~~~~~~
        'removeCoordinatedTimer' is declared here.
    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:383:33 - error TS2445: Property 'forceCleanup' is protected and only accessible within class 'MemorySafeResourceManager' and its subclasses.

    383       await memorySafetyManager.forceCleanup();
                                        ~~~~~~~~~~~~
    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:414:26 - error TS2551: Property 'createCoordinatedTimer' does not exist on type 'TimerCoordinationService'. Did you mean 'removeCoordinatedTimer'?

    414         timerCoordinator.createCoordinatedTimer(() => {}, 100, `perf-timer-${i}`);
                                 ~~~~~~~~~~~~~~~~~~~~~~

      shared/src/base/TimerCoordinationService.ts:559:10
        559   public removeCoordinatedTimer(compositeId: string): void {
                     ~~~~~~~~~~~~~~~~~~~~~~
        'removeCoordinatedTimer' is declared here.
    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:468:23 - error TS2551: Property 'unregisterAllHandlers' does not exist on type 'EventHandlerRegistry'. Did you mean 'unregisterHandler'?

    468         eventRegistry.unregisterAllHandlers(`load-client-${loadSize - 1}`);
                              ~~~~~~~~~~~~~~~~~~~~~

      shared/src/base/EventHandlerRegistry.ts:331:10
        331   public unregisterHandler(handlerId: string): boolean {
                     ~~~~~~~~~~~~~~~~~
        'unregisterHandler' is declared here.
    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:499:26 - error TS2551: Property 'createCoordinatedTimer' does not exist on type 'TimerCoordinationService'. Did you mean 'removeCoordinatedTimer'?

    499         timerCoordinator.createCoordinatedTimer(
                                 ~~~~~~~~~~~~~~~~~~~~~~

      shared/src/base/TimerCoordinationService.ts:559:10
        559   public removeCoordinatedTimer(compositeId: string): void {
                     ~~~~~~~~~~~~~~~~~~~~~~
        'removeCoordinatedTimer' is declared here.
    shared/src/base/__tests__/MemorySafeSystem.integration.test.ts:506:31 - error TS2551: Property 'unregisterAllHandlers' does not exist on type 'EventHandlerRegistry'. Did you mean 'unregisterHandler'?

    506                 eventRegistry.unregisterAllHandlers(clientId);
                                      ~~~~~~~~~~~~~~~~~~~~~

      shared/src/base/EventHandlerRegistry.ts:331:10
        331   public unregisterHandler(handlerId: string): boolean {
                     ~~~~~~~~~~~~~~~~~
        'unregisterHandler' is declared here.

Test Suites: 1 failed, 1 total
Tests:       0 total
Snapshots:   0 total
Time:        3.774 s
Ran all test suites matching /MemorySafeSystem.integration.test.ts/i.
