oa-prod$ npm test -- --testPathPattern="shared/src/base" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=shared/src/base --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.performance.test.ts (225 MB heap size)
  AtomicCircularBuffer - Performance
    Performance Benchmarks
      ✓ should maintain fast add operations under load (7 ms)
      ✓ should maintain fast get operations under load (50 ms)
      ✓ should maintain fast remove operations under load (6 ms)
      ✓ should handle mixed operations efficiently (4 ms)
    Concurrent Access Performance
      ✓ should handle basic concurrent operations (18 ms)
      ✓ should handle concurrent additions without corruption (13 ms)
      ✓ should handle rapid key updates without data loss (7 ms)
    Stress Testing
      ✓ should handle high-concurrency stress test (8 ms)
      ✓ should maintain performance with large datasets (56 ms)
    Performance Metrics
      ✓ should track operation metrics accurately (2 ms)
      ✓ should provide performance insights through metrics (2 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.test.ts (243 MB heap size)
  AtomicCircularBuffer
    Memory Boundary Enforcement
      ✓ should enforce maximum size limits (3 ms)
      ✓ should maintain size consistency under rapid additions (2 ms)
      ✓ should handle automatic cleanup correctly (2 ms)
    Async Operations
      ✓ should handle addItem async operations correctly (2 ms)
      ✓ should handle removeItem async operations correctly (2 ms)
      ✓ should handle clear operations correctly (3 ms)
      ✓ should handle shutdown operations correctly (13 ms)
    Enhanced Concurrent Access and Race Conditions
      Basic concurrent operations
        ✓ should handle concurrent additions without corruption (21 ms)
        ✓ should handle mixed concurrent operations (12 ms)
        ✓ should handle concurrent access during shutdown (2 ms)
      Advanced race condition scenarios
        ✓ should handle rapid key updates without data loss (6 ms)
        ✓ should handle concurrent add/remove on same keys (16 ms)
        ✓ should handle concurrent operations with buffer overflow (8 ms)
        ✓ should handle concurrent clear operations (3 ms)
      Stress testing and performance under concurrency
        ✓ should handle high-concurrency stress test (6 ms)
        ✓ should maintain performance under sustained load (test environment) (2 ms)
        ✓ should handle concurrent operations with different data types (4 ms)
      Edge cases in concurrent scenarios
        ✓ should handle concurrent operations on empty buffer (6 ms)
        ✓ should handle concurrent operations during buffer state transitions (4 ms)
    Memory Pressure Handling
      ✓ should handle memory pressure gracefully (14 ms)
      ✓ should maintain performance under load (2 ms)
    Comprehensive Metrics Testing
      Basic metrics tracking
        ✓ should initialize metrics with zero values (2 ms)
        ✓ should track add operations correctly (2 ms)
        ✓ should track remove operations correctly (2 ms)
        ✓ should track clear operations correctly (1 ms)
        ✓ should track automatic cleanup operations (1 ms)
      Metrics defensive copying
        ✓ should return defensive copy of metrics (2 ms)
        ✓ should handle metrics during concurrent access (9 ms)
      Metrics edge cases and overflow scenarios
        ✓ should handle large operation counts (6 ms)
        ✓ should maintain metrics accuracy during buffer overflow (2 ms)
        ✓ should handle metrics during rapid operations (26 ms)
        ✓ should reset metrics appropriately on clear (2 ms)
      Sync validation metrics
        ✓ should track sync validations (1 ms)
        ✓ should initialize sync error tracking correctly (1 ms)
    Resource Limits and Integration
      ✓ should integrate properly with MemorySafeResourceManager (3 ms)
      ✓ should handle resource exhaustion gracefully (2 ms)
    Basic Functionality - Enhanced Coverage
      Constructor and Initialization
        ✓ should create buffer with valid max size (2 ms)
        ✓ should handle edge case max sizes (2 ms)
        ✓ should handle zero max size gracefully (2 ms)
      getItem method edge cases
        ✓ should handle non-existent keys (2 ms)
        ✓ should handle special character keys (3 ms)
        ✓ should handle unicode keys (2 ms)
      getAllItems method edge cases
        ✓ should return empty map when buffer is empty (2 ms)
        ✓ should return defensive copy of items (2 ms)
        ✓ should handle buffer at max capacity (3 ms)
      getSize method edge cases
        ✓ should return 0 for empty buffer (2 ms)
        ✓ should track size accurately during operations (2 ms)
        ✓ should maintain size consistency during overflow (2 ms)
    Error Conditions and Edge Cases
      ✓ should handle operations on uninitialized buffer (2 ms)
      ✓ should handle double initialization gracefully (2 ms)
      ✓ should handle double shutdown gracefully (2 ms)
      ✓ should handle operations after shutdown (1 ms)
      ✓ should handle invalid keys and values (2 ms)
      ✓ should handle extremely long keys (2 ms)
      ✓ should handle duplicate key additions (1 ms)
      ✓ should handle removing non-existent items (1 ms)
    Comprehensive Error Handling and Recovery
      Invalid input handling
        ✓ should handle null and undefined keys gracefully (2 ms)
        ✓ should handle extremely large keys (3 ms)
        ✓ should handle keys with problematic characters (3 ms)
        ✓ should handle circular reference values (2 ms)
        ✓ should handle extremely large values (8 ms)
      Buffer state error conditions
        ✓ should handle operations on corrupted buffer state (4 ms)
        ✓ should handle rapid state changes (2 ms)
        ✓ should handle buffer overflow edge cases (4 ms)
      Resource exhaustion scenarios
        ✓ should handle memory pressure gracefully (5 ms)
        ✓ should handle timer/interval resource limits (5 ms)
      Recovery and resilience
        ✓ should recover from temporary errors (2 ms)
        ✓ should handle graceful degradation (2 ms)
        ✓ should maintain data integrity during errors (2 ms)
    Logging Interface (ILoggingService)
      logInfo method
        ✓ should log info messages correctly (2 ms)
        ✓ should log info messages with details (1 ms)
        ✓ should handle empty info messages (1 ms)
        ✓ should handle complex details objects (1 ms)
      logWarning method
        ✓ should log warning messages correctly (1 ms)
        ✓ should log warning messages with details (1 ms)
        ✓ should handle special characters in warning messages (1 ms)
      logError method
        ✓ should log error messages with Error objects (3 ms)
        ✓ should log error messages with string errors (2 ms)
        ✓ should log error messages with details (2 ms)
        ✓ should handle null/undefined errors (2 ms)
        ✓ should handle complex error objects (2 ms)
      logDebug method
        ✓ should log debug messages in development environment (2 ms)
        ✓ should log debug messages when DEBUG flag is set (2 ms)
        ✓ should not log debug messages in production without DEBUG flag (2 ms)
        ✓ should log debug messages with details (2 ms)
      Logging integration with buffer operations
        ✓ should not interfere with normal buffer operations (2 ms)
        ✓ should handle logging during concurrent operations (6 ms)
    Performance and Memory Benchmarks
      Performance benchmarks
        ✓ should maintain fast add operations under load (2 ms)
        ✓ should maintain fast remove operations under load (5 ms)
        ✓ should maintain fast read operations under load (2 ms)
        ✓ should handle mixed operation performance (4 ms)
        ✓ should scale performance with buffer size (4 ms)
      Memory usage and leak detection
        ✓ should not leak memory during normal operations (2 ms)
        ✓ should maintain stable memory usage under sustained load (4 ms)
        ✓ should clean up memory on buffer clear (8 ms)
        ✓ should clean up all resources on shutdown (2 ms)
      Resource cleanup verification
        ✓ should properly clean up intervals and timeouts (2 ms)
        ✓ should handle multiple buffer instances without resource conflicts (3 ms)
        ✓ should handle rapid create/destroy cycles (3 ms)
    Synchronization and Validation
      Internal state consistency
        ✓ should maintain consistency between items map and insertion order (2 ms)
        ✓ should handle rapid add/remove cycles without corruption (8 ms)
        ✓ should maintain order integrity during overflow (2 ms)
      Concurrent access synchronization
        ✓ should handle sequential add operations without state corruption (2 ms)
        ✓ should handle mixed sequential operations safely (1 ms)
        ✓ should handle concurrent operations during buffer overflow (7 ms)
      Lock mechanism validation
        ✓ should execute operations sequentially with operation lock (2 ms)
        ✓ should handle sequential operations efficiently (2 ms)
      Validation and error detection
        ✓ should track sync validations in metrics (1 ms)
        ✓ should handle validation during high-frequency operations (24 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/MemorySafeResourceManager.test.ts (240 MB heap size)
  MemorySafeResourceManager
    Initialization and Lifecycle
      ✓ should initialize correctly with default limits (2 ms)
      ✓ should initialize correctly with custom limits (2 ms)
      ✓ should handle double initialization gracefully (2 ms)
      ✓ should shutdown correctly (2 ms)
      ✓ should handle double shutdown gracefully (2 ms)
      ✓ should prevent operations during shutdown (2 ms)
    Resource Creation and Management
      Safe Interval Creation
        ✓ should create safe intervals correctly (3 ms)
        ✓ should enforce interval limits (3 ms)
        ✓ should handle interval errors gracefully (6 ms)
      Safe Timeout Creation
        ✓ should create safe timeouts correctly (3 ms)
        ✓ should enforce timeout limits (2 ms)
        ✓ should auto-cleanup timeouts after execution (2 ms)
      Shared Resource Management
        ✓ should create and manage shared resources (2 ms)
        ✓ should handle reference counting correctly (2 ms)
    Resource Limits and Enforcement
      ✓ should track resource metrics correctly (3 ms)
      ✓ should report health status correctly (2 ms)
      ✓ should handle memory pressure detection (2 ms)
    Cleanup and Resource Management
      ✓ should perform periodic cleanup (2 ms)
      ✓ should force cleanup all resources (2 ms)
      ✓ should handle emergency cleanup correctly (4 ms)
    Error Handling and Edge Cases
      ✓ should handle resource creation errors gracefully (2 ms)
      ✓ should handle cleanup errors gracefully (25 ms)
      ✓ should handle operations on uninitialized manager (1 ms)
      ✓ should handle concurrent operations safely (2 ms)
    Memory Leak Detection
      ✓ should not leak memory during normal operations (2 ms)
      ✓ should clean up global instances on process events (1 ms)
    Singleton Factory
      ✓ should create singleton instances correctly (1 ms)
      ✓ should recreate singleton after shutdown (2 ms)
    Integration with Event System
      ✓ should emit lifecycle events correctly (1 ms)
      ✓ should emit error events for resource errors (1 ms)
    Performance Benchmarks
      ✓ should create resources within performance bounds (4 ms)
      ✓ should handle resource cleanup within performance bounds (2 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.memory.test.ts (247 MB heap size)
  AtomicCircularBuffer - Memory Safety
    Memory Leak Detection
      ✓ should not leak memory during normal operations (3 ms)
      ✓ should properly clean up large datasets (63 ms)
      ✓ should handle memory pressure gracefully (3 ms)
    Resource Cleanup Verification
      ✓ should properly clean up intervals and timeouts (2 ms)
      ✓ should handle shutdown during active operations (12 ms)
    Memory Boundary Enforcement
      ✓ should enforce maximum size limits (2 ms)
      ✓ should handle zero-size buffer gracefully (2 ms)
    Memory Monitoring Integration
      ✓ should provide accurate resource metrics (2 ms)
      ✓ should track memory usage patterns (2 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/TimerCoordinationService.test.ts (255 MB heap size)
  TimerCoordinationService
    Singleton Pattern
      ✓ should return same instance when called multiple times (2 ms)
      ✓ should work with getTimerCoordinator helper (1 ms)
      ✓ should maintain configuration across getInstance calls (5 ms)
    Timer Creation and Management
      Coordinated Interval Creation
        ✓ should create coordinated intervals successfully (2 ms)
        ✓ should prevent duplicate timer creation (1 ms)
        ✓ should allow forced timer recreation (2 ms)
        ✓ should adjust intervals for test environments (2 ms)
      Timer Limits and Enforcement
        ✓ should enforce per-service timer limits (18 ms)
        ✓ should enforce global timer limits (2 ms)
        ✓ should track timers per service correctly (2 ms)
      Timer Removal
        ✓ should remove coordinated timers correctly (2 ms)
        ✓ should handle removal of non-existent timers gracefully (5 ms)
        ✓ should update service counts when removing timers (2 ms)
    Timer Statistics and Monitoring
      ✓ should provide accurate timer statistics (2 ms)
      ✓ should track timer execution counts (2 ms)
      ✓ should identify oldest and most active timers (3 ms)
    Error Handling and Edge Cases
      ✓ should handle callback errors gracefully (1 ms)
      ✓ should handle invalid timer parameters (2 ms)
      ✓ should handle empty or invalid service IDs (2 ms)
      ✓ should handle concurrent timer operations (1 ms)
    Integration with MemorySafeResourceManager
      ✓ should integrate properly with base class functionality (1 ms)
      ✓ should handle initialization lifecycle correctly (2 ms)
      ✓ should handle shutdown lifecycle correctly (1 ms)
    Memory Leak Prevention
      ✓ should not leak memory during normal operations (4 ms)
      ✓ should clean up timer metadata on removal (2 ms)
      ✓ should handle rapid timer creation and removal (1 ms)
    Environment-Specific Behavior
      ✓ should handle test environment configurations (2 ms)
      ✓ should apply different limits in different environments (2 ms)
    Performance Benchmarks
      ✓ should create timers within performance bounds (1 ms)
      ✓ should handle timer removal within performance bounds (1 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/LoggingMixin.test.ts (260 MB heap size)
  LoggingMixin
    SimpleLogger
      Basic Logging Functionality
        ✓ should log info messages correctly (2 ms)
        ✓ should log warning messages correctly (1 ms)
        ✓ should log error messages correctly with Error objects (1 ms)
        ✓ should log error messages correctly with string errors (2 ms)
        ✓ should handle undefined details gracefully (2 ms)
      Debug Logging Behavior
        ✓ should log debug messages in development environment (2 ms)
        ✓ should log debug messages when DEBUG env var is set (1 ms)
        ✓ should not log debug messages in production without DEBUG flag (2 ms)
      Memory-Safe Logging
        ✓ should handle large details objects without memory issues (2 ms)
        ✓ should handle circular reference objects safely (5 ms)
        ✓ should handle null and undefined errors correctly (2 ms)
      Performance Impact
        ✓ should complete high-volume logging within reasonable time (4 ms)
        ✓ should handle concurrent logging without corruption (3 ms)
    withLogging Mixin
      Mixin Integration
        ✓ should add logging functionality to base class (2 ms)
        ✓ should implement ILoggingService interface correctly (1 ms)
        ✓ should preserve base class inheritance chain (2 ms)
      Mixin Logging Behavior
        ✓ should log with correct service name prefix (2 ms)
        ✓ should handle all log levels through mixin (2 ms)
      Multiple Mixin Instances
        ✓ should handle multiple service instances with different names (2 ms)
        ✓ should maintain separate logging contexts (2 ms)
    Integration with BaseTrackingService
      ✓ should integrate seamlessly with service inheritance patterns (2 ms)
      ✓ should handle service lifecycle with logging (3 ms)
    Memory Leak Prevention
      ✓ should not retain references after logging (2 ms)
      ✓ should handle rapid creation and destruction of loggers (1 ms)
    Error Handling and Edge Cases
      ✓ should handle empty service names gracefully (1 ms)
      ✓ should handle special characters in service names (1 ms)
      ✓ should handle extremely long messages (1 ms)
      ✓ should handle console method failures gracefully (15 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/EventHandlerRegistry.test.ts (259 MB heap size)
  EventHandlerRegistry
    Basic Functionality
      ✓ should create registry with valid configuration (2 ms)
      ✓ should register handlers successfully (2 ms)
      ✓ should unregister handlers successfully (2 ms)
      ✓ should return false when unregistering non-existent handler (1 ms)
    Handler Retrieval
      ✓ should retrieve handlers for specific event type (1 ms)
      ✓ should return empty array for non-existent event type (1 ms)
      ✓ should update lastUsed timestamp when retrieving handlers (3 ms)
    Client Management
      ✓ should unregister all handlers for a client (2 ms)
      ✓ should return 0 when unregistering handlers for non-existent client (2 ms)
      ✓ should enforce client handler limits (23 ms)
    Metrics and Monitoring
      ✓ should track handler metrics correctly (2 ms)
      ✓ should initialize metrics with zero values (2 ms)
    Error Handling
      ✓ should throw error for invalid registration parameters (3 ms)
      ✓ should handle registration with metadata (2 ms)
    Memory Safety
      ✓ should perform emergency cleanup when global limit exceeded (2 ms)
      ✓ should not leak memory during normal operations (2 ms)
    Logging Interface
      ✓ should log info messages correctly (1 ms)
      ✓ should log warning messages correctly (2 ms)
      ✓ should log error messages correctly (1 ms)
      ✓ should log debug messages correctly (1 ms)
    Global Instance Management
      ✓ should provide global instance access (2 ms)
      ✓ should return same instance on multiple calls (4 ms)
      ✓ should reset global instance correctly (2 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.core.test.ts (273 MB heap size)
  AtomicCircularBuffer - Core Functionality
    Constructor and Initialization
      ✓ should create buffer with valid max size (2 ms)
      ✓ should initialize with empty state (1 ms)
      ✓ should handle zero max size (2 ms)
      ✓ should handle large max size (1 ms)
    Basic Add Operations
      ✓ should add single item correctly (1 ms)
      ✓ should add multiple items correctly (2 ms)
      ✓ should handle duplicate keys by updating values (2 ms)
      ✓ should handle empty string keys (1 ms)
      ✓ should handle special character keys (2 ms)
    Basic Remove Operations
      ✓ should remove existing item correctly (2 ms)
      ✓ should return false for non-existent key (2 ms)
      ✓ should handle removing all items (2 ms)
    Basic Get Operations
      ✓ should retrieve existing items correctly (2 ms)
      ✓ should return undefined for non-existent keys (2 ms)
      ✓ should handle getAllItems correctly (2 ms)
      ✓ should return defensive copy of all items (5 ms)
    Size Management
      ✓ should track size accurately (1 ms)
      ✓ should enforce maximum size limits (1 ms)
      ✓ should handle size correctly with duplicate keys (2 ms)
    Circular Buffer Behavior
      ✓ should implement circular behavior when full (2 ms)
      ✓ should maintain insertion order in circular behavior (2 ms)
    Resource Management Integration
      ✓ should integrate with MemorySafeResourceManager (2 ms)
      ✓ should handle initialization and shutdown properly (2 ms)
    Basic Metrics
      ✓ should initialize metrics with zero values (2 ms)
      ✓ should track basic operations in metrics (2 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.integration.test.ts (274 MB heap size)
  AtomicCircularBuffer - Integration
    MemorySafeResourceManager Integration
      ✓ should integrate properly with MemorySafeResourceManager (3 ms)
      ✓ should handle resource lifecycle properly (3 ms)
      ✓ should handle shared resource scenarios (2 ms)
    Logging Integration
      ✓ should implement ILoggingService interface correctly (2 ms)
      ✓ should integrate logging with buffer operations (2 ms)
    Error Handling Integration
      ✓ should handle operations on uninitialized buffer (2 ms)
      ✓ should handle invalid input gracefully (2 ms)
      ✓ should recover from temporary errors (4 ms)
    End-to-End Workflow Scenarios
      ✓ should handle complete lifecycle workflow (2 ms)
      ✓ should handle concurrent workflow scenarios (5 ms)
      ✓ should handle stress workflow with recovery (5 ms)
    Cross-Component Integration
      ✓ should work with multiple buffer instances (2 ms)

Test Suites: 9 passed, 9 total
Tests:       279 passed, 279 total
Snapshots:   0 total
Time:        3.497 s, estimated 4 s
Ran all test suites matching /shared\/src\/base/i