oa-prod$ npm test -- --testPathPattern="CleanupCoordinator.test.ts" --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=CleanupCoordinator.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/CleanupCoordinator.test.ts (393.31 s, 366 MB heap size)
  CleanupCoordinator
    Basic Operation Management
      ✕ should schedule and execute cleanup operations (30009 ms)
      ✕ should handle operation priorities correctly (30009 ms)
      ✕ should cancel queued operations (30009 ms)
    Concurrent Operation Testing
      ✕ should handle multiple concurrent operations (30006 ms)
      ✕ should handle operation failures and retries (30004 ms)
      ✕ should handle permanent failures after max retries (30007 ms)
    Conflict Prevention Validation
      ✕ should detect and prevent conflicting operations (30008 ms)
      ✕ should handle component-level locking (30006 ms)
    Dependency Management
      ✕ should handle operation dependencies correctly (30004 ms)
    Metrics and Monitoring
      ✕ should track operation metrics accurately (30005 ms)
      ✕ should track operation types and priorities (30004 ms)
    Force Cleanup
      ✕ should execute force cleanup immediately (30007 ms)
      ✕ should handle force cleanup failures (30003 ms)

  ● CleanupCoordinator › Basic Operation Management › should schedule and execute cleanup operations

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      70 |
      71 |   describe('Basic Operation Management', () => {
    > 72 |     it('should schedule and execute cleanup operations', async () => {
         |     ^
      73 |       let executed = false;
      74 |       const operation = async () => {
      75 |         executed = true;

      at shared/src/base/__tests__/CleanupCoordinator.test.ts:72:5
      at shared/src/base/__tests__/CleanupCoordinator.test.ts:71:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinator.test.ts:35:1)

  ● CleanupCoordinator › Basic Operation Management › should handle operation priorities correctly

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      92 |     });
      93 |
    > 94 |     it('should handle operation priorities correctly', async () => {
         |     ^
      95 |       const executionOrder: string[] = [];
      96 |
      97 |       // Schedule operations with different priorities

      at shared/src/base/__tests__/CleanupCoordinator.test.ts:94:5
      at shared/src/base/__tests__/CleanupCoordinator.test.ts:71:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinator.test.ts:35:1)

  ● CleanupCoordinator › Basic Operation Management › should cancel queued operations

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      135 |     });
      136 |
    > 137 |     it('should cancel queued operations', async () => {
          |     ^
      138 |       let executed = false;
      139 |       const operation = async () => {
      140 |         executed = true;

      at shared/src/base/__tests__/CleanupCoordinator.test.ts:137:5
      at shared/src/base/__tests__/CleanupCoordinator.test.ts:71:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinator.test.ts:35:1)

  ● CleanupCoordinator › Concurrent Operation Testing › should handle multiple concurrent operations

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      160 |
      161 |   describe('Concurrent Operation Testing', () => {
    > 162 |     it('should handle multiple concurrent operations', async () => {
          |     ^
      163 |       const executionCount = { value: 0 };
      164 |       const maxConcurrent = { value: 0 };
      165 |       const currentConcurrent = { value: 0 };

      at shared/src/base/__tests__/CleanupCoordinator.test.ts:162:5
      at shared/src/base/__tests__/CleanupCoordinator.test.ts:161:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinator.test.ts:35:1)

  ● CleanupCoordinator › Concurrent Operation Testing › should handle operation failures and retries

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      201 |     });
      202 |
    > 203 |     it('should handle operation failures and retries', async () => {
          |     ^
      204 |       let attemptCount = 0;
      205 |       const operation = async () => {
      206 |         attemptCount++;

      at shared/src/base/__tests__/CleanupCoordinator.test.ts:203:5
      at shared/src/base/__tests__/CleanupCoordinator.test.ts:161:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinator.test.ts:35:1)

  ● CleanupCoordinator › Concurrent Operation Testing › should handle permanent failures after max retries

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      225 |     });
      226 |
    > 227 |     it('should handle permanent failures after max retries', async () => {
          |     ^
      228 |       let attemptCount = 0;
      229 |       const operation = async () => {
      230 |         attemptCount++;

      at shared/src/base/__tests__/CleanupCoordinator.test.ts:227:5
      at shared/src/base/__tests__/CleanupCoordinator.test.ts:161:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinator.test.ts:35:1)

  ● CleanupCoordinator › Conflict Prevention Validation › should detect and prevent conflicting operations

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      248 |
      249 |   describe('Conflict Prevention Validation', () => {
    > 250 |     it('should detect and prevent conflicting operations', async () => {
          |     ^
      251 |       const executionOrder: string[] = [];
      252 |
      253 |       // Schedule a shutdown operation (conflicts with everything)

      at shared/src/base/__tests__/CleanupCoordinator.test.ts:250:5
      at shared/src/base/__tests__/CleanupCoordinator.test.ts:249:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinator.test.ts:35:1)

  ● CleanupCoordinator › Conflict Prevention Validation › should handle component-level locking

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      284 |     });
      285 |
    > 286 |     it('should handle component-level locking', async () => {
          |     ^
      287 |       const executionOrder: string[] = [];
      288 |       const componentId = 'shared-component';
      289 |

      at shared/src/base/__tests__/CleanupCoordinator.test.ts:286:5
      at shared/src/base/__tests__/CleanupCoordinator.test.ts:249:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinator.test.ts:35:1)

  ● CleanupCoordinator › Dependency Management › should handle operation dependencies correctly

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      319 |
      320 |   describe('Dependency Management', () => {
    > 321 |     it('should handle operation dependencies correctly', async () => {
          |     ^
      322 |       const executionOrder: string[] = [];
      323 |
      324 |       // Schedule dependent operation first

      at shared/src/base/__tests__/CleanupCoordinator.test.ts:321:5
      at shared/src/base/__tests__/CleanupCoordinator.test.ts:320:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinator.test.ts:35:1)

  ● CleanupCoordinator › Metrics and Monitoring › should track operation metrics accurately

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      358 |
      359 |   describe('Metrics and Monitoring', () => {
    > 360 |     it('should track operation metrics accurately', async () => {
          |     ^
      361 |       const initialMetrics = coordinator.getMetrics();
      362 |       expect(initialMetrics.totalOperations).toBe(0);
      363 |

      at shared/src/base/__tests__/CleanupCoordinator.test.ts:360:5
      at shared/src/base/__tests__/CleanupCoordinator.test.ts:359:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinator.test.ts:35:1)

  ● CleanupCoordinator › Metrics and Monitoring › should track operation types and priorities

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      386 |     });
      387 |
    > 388 |     it('should track operation types and priorities', async () => {
          |     ^
      389 |       // Schedule operations of different types and priorities
      390 |       coordinator.scheduleCleanup(
      391 |         CleanupOperationType.TIMER_CLEANUP,

      at shared/src/base/__tests__/CleanupCoordinator.test.ts:388:5
      at shared/src/base/__tests__/CleanupCoordinator.test.ts:359:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinator.test.ts:35:1)

  ● CleanupCoordinator › Force Cleanup › should execute force cleanup immediately

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      414 |
      415 |   describe('Force Cleanup', () => {
    > 416 |     it('should execute force cleanup immediately', async () => {
          |     ^
      417 |       let executed = false;
      418 |       const operation = async () => {
      419 |         executed = true;

      at shared/src/base/__tests__/CleanupCoordinator.test.ts:416:5
      at shared/src/base/__tests__/CleanupCoordinator.test.ts:415:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinator.test.ts:35:1)

  ● CleanupCoordinator › Force Cleanup › should handle force cleanup failures

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      433 |     });
      434 |
    > 435 |     it('should handle force cleanup failures', async () => {
          |     ^
      436 |       const operation = async () => {
      437 |         throw new Error('Force cleanup failed');
      438 |       };

      at shared/src/base/__tests__/CleanupCoordinator.test.ts:435:5
      at shared/src/base/__tests__/CleanupCoordinator.test.ts:415:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinator.test.ts:35:1)

Test Suites: 1 failed, 1 total
Tests:       13 failed, 13 total
Snapshots:   0 total
Time:        393.567 s
Ran all test suites matching /CleanupCoordinator.test.ts/i.