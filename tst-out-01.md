oa-prod$ npm test -- --testPathPattern="EventHandlerRegistry.test.ts" --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=EventHandlerRegistry.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/EventHandlerRegistry.test.ts (379 MB heap size)
  EventHandlerRegistry
    Basic Functionality
      ✓ should create registry with valid configuration (5 ms)
      ✓ should register handlers successfully (7 ms)
      ✓ should unregister handlers successfully (2 ms)
      ✓ should return false when unregistering non-existent handler (2 ms)
    Handler Retrieval
      ✓ should retrieve handlers for specific event type (3 ms)
      ✓ should return empty array for non-existent event type (2 ms)
      ✓ should update lastUsed timestamp when retrieving handlers (4 ms)
    Client Management
      ✓ should unregister all handlers for a client (4 ms)
      ✓ should return 0 when unregistering handlers for non-existent client (2 ms)
      ✓ should enforce client handler limits (35 ms)
    Metrics and Monitoring
      ✓ should track handler metrics correctly (3 ms)
      ✓ should initialize metrics with zero values (2 ms)
    Error Handling
      ✓ should throw error for invalid registration parameters (3 ms)
      ✓ should handle registration with metadata (4 ms)
    Memory Safety
      ✓ should perform emergency cleanup when global limit exceeded (2 ms)
      ✓ should not leak memory during normal operations (2 ms)
    Logging Interface
      ✓ should log info messages correctly (1 ms)
      ✓ should log warning messages correctly (1 ms)
      ✓ should log error messages correctly (2 ms)
      ✓ should log debug messages correctly (9 ms)
    Global Instance Management
      ✓ should provide global instance access (2 ms)
      ✓ should return same instance on multiple calls (2 ms)
      ✓ should reset global instance correctly (2 ms)

Test Suites: 1 passed, 1 total
Tests:       23 passed, 23 total
Snapshots:   0 total
Time:        3.449 s



oa-prod$ npm test -- --testPathPattern="EventHandlerRegistry.integration.test.ts" --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=EventHandlerRegistry.integration.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/EventHandlerRegistry.integration.test.ts
  ● Test suite failed to run

    shared/src/base/__tests__/EventHandlerRegistry.integration.test.ts:49:16 - error TS2673: Constructor of class 'EventHandlerRegistry' is private and only accessible within the class declaration.

     49     registry = new EventHandlerRegistry({
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~
     50       maxHandlersPerClient: 100,
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    ...
     53       maxHandlerAgeMs: 5000 // 5 seconds for testing
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     54     });
        ~~~~~~

Test Suites: 1 failed, 1 total
Tests:       0 total
Snapshots:   0 total
Time:        3.26 s
Ran all test suites matching /EventHandlerRegistry.integration.test.ts/i.
