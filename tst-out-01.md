oa-prod$ npm test -- --testPathPattern="CleanupCoordinator.test.ts" --verbose --no-coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=CleanupCoordinator.test.ts --verbose --no-coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/CleanupCoordinator.test.ts (234 MB heap size)
  CleanupCoordinator
    Basic Operation Management
      ✓ should schedule and execute cleanup operations (27 ms)
      ✓ should handle operation priorities correctly (4 ms)
      ✓ should cancel queued operations (7 ms)
    Concurrent Operation Testing
      ✓ should handle multiple concurrent operations (7 ms)
      ✓ should handle operation failures and retries (5 ms)
      ✓ should handle permanent failures after max retries (4 ms)
    Metrics and Monitoring
      ✓ should track operation metrics accurately (4 ms)
      ✓ should track operation types and priorities (8 ms)
      ✓ should track operation metrics accurately (7 ms)
      ✓ should track operation types and priorities (4 ms)
    Basic Functionality
      ✓ should provide basic cleanup coordination (4 ms)
    Conflict Prevention Validation
      ✕ should detect and prevent conflicting operations (6 ms)
      ✓ should handle component-level locking (3 ms)
    Dependency Management
      ✓ should handle operation dependencies correctly (3 ms)
    Force Cleanup
      ✓ should execute force cleanup immediately (3 ms)
      ✓ should handle force cleanup failures (13 ms)
    Memory Leak Prevention
      ✓ should prevent memory leaks during cleanup cycles (23 ms)

  ● CleanupCoordinator › Conflict Prevention Validation › should detect and prevent conflicting operations

    expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      371 |       // Check that conflicts were detected
      372 |       const metrics = coordinator.getMetrics();
    > 373 |       expect(metrics.conflictsPrevented).toBeGreaterThan(0);
          |                                          ^
      374 |     });
      375 |
      376 |     it('should handle component-level locking', async () => {

      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinator.test.ts:373:42)

Test Suites: 1 failed, 1 total
Tests:       1 failed, 16 passed, 17 total
Snapshots:   0 total
Time:        2.166 s
Ran all test suites matching /CleanupCoordinator.test.ts/i.
