oa-prod$ npm test -- --testPathPattern=EventHandlerRegistry.test.ts --verbose --no-coverage --forceExit

> oa-framework@1.0.0 test
> jest --testPathPattern=EventHandlerRegistry.test.ts --verbose --no-coverage --forceExit

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/EventHandlerRegistry.test.ts (380 MB heap size)
  EventHandlerRegistry
    Basic Functionality
      ✓ should create registry with valid configuration (11 ms)
      ✓ should register handlers successfully (5 ms)
      ✓ should unregister handlers successfully (3 ms)
      ✓ should return false when unregistering non-existent handler (2 ms)
    Handler Retrieval
      ✓ should retrieve handlers for specific event type (3 ms)
      ✓ should return empty array for non-existent event type (2 ms)
      ✓ should update lastUsed timestamp when retrieving handlers (9 ms)
    Client Management
      ✓ should unregister all handlers for a client (2 ms)
      ✓ should return 0 when unregistering handlers for non-existent client (3 ms)
      ✓ should enforce client handler limits (20 ms)
    Metrics and Monitoring
      ✓ should track handler metrics correctly (5 ms)
      ✓ should initialize metrics with zero values (4 ms)
    Error Handling
      ✓ should throw error for invalid registration parameters (4 ms)
      ✓ should handle registration with metadata (4 ms)
    Memory Safety
      ✓ should perform emergency cleanup when global limit exceeded (8 ms)
      ✓ should not leak memory during normal operations (2 ms)
    Logging Interface
      ✓ should log info messages correctly (2 ms)
      ✓ should log warning messages correctly (2 ms)
      ✓ should log error messages correctly (2 ms)
      ✓ should log debug messages correctly (3 ms)
    Global Instance Management
      ✓ should provide global instance access (2 ms)
      ✓ should return same instance on multiple calls (1 ms)
      ✓ should reset global instance correctly (2 ms)

Test Suites: 1 passed, 1 total
Tests:       23 passed, 23 total
Snapshots:   0 total
Time:        3.503 s
Ran all test suites matching /EventHandlerRegistry.test.ts/i.
