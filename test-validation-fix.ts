/**
 * Test to verify the Service Lifecycle Compatibility fix
 * Tests that validation returns 'valid' status after tracking operations
 */

import { BaseTrackingService } from './server/src/platform/tracking/core-data/base/BaseTrackingService';
import { TTrackingData, TValidationResult } from './shared/src/types/platform/tracking/core/tracking-data-types';

class TestValidationFixService extends BaseTrackingService {
  protected getServiceName(): string {
    return 'test-validation-fix-service';
  }

  protected getServiceVersion(): string {
    return '1.0.0-validation-fix';
  }

  protected async doTrack(data: TTrackingData): Promise<void> {
    // Simple tracking implementation
    this.incrementCounter('test_operations');
    this.updatePerformanceMetric('test_operation_time', Date.now());
  }

  protected async doValidate(): Promise<TValidationResult> {
    return {
      validationId: this.generateId(),
      componentId: this.getServiceName(),
      timestamp: new Date(),
      executionTime: 5,
      status: 'valid', // Return valid status
      overallScore: 95,
      checks: [],
      references: {
        componentId: this.getServiceName(),
        internalReferences: [],
        externalReferences: [],
        circularReferences: [],
        missingReferences: [],
        redundantReferences: [],
        metadata: {
          totalReferences: 0,
          buildTimestamp: new Date(),
          analysisDepth: 1
        }
      },
      recommendations: [],
      warnings: [],
      errors: [],
      metadata: {
        validationMethod: 'validation-fix-test',
        rulesApplied: 1,
        dependencyDepth: 0,
        cyclicDependencies: [],
        orphanReferences: []
      }
    };
  }

  // Helper method to create valid tracking data
  private createTrackingData(componentId: string, complianceScore: number = 85): TTrackingData {
    return {
      componentId,
      status: 'in-progress',
      timestamp: new Date().toISOString(),
      metadata: {
        phase: 'testing',
        progress: 50,
        priority: 'P1',
        tags: ['validation-fix', 'test'],
        custom: {}
      },
      context: {
        contextId: 'validation-fix-context',
        milestone: 'validation-fix-test',
        category: 'infrastructure',
        dependencies: [],
        dependents: []
      },
      progress: {
        completion: 50,
        tasksCompleted: 5,
        totalTasks: 10,
        timeSpent: 300,
        estimatedTimeRemaining: 300,
        quality: {
          codeCoverage: 85,
          testCount: 20,
          bugCount: 0,
          qualityScore: 92,
          performanceScore: 94
        }
      },
      authority: {
        validator: 'validation-fix-test-validator',
        level: 'architectural-authority',
        complianceScore,
        validationStatus: 'validated',
        validatedAt: new Date().toISOString()
      }
    };
  }

  // Public method to test the fix
  public async testValidationFix(): Promise<boolean> {
    try {
      // Initialize service
      await this.initialize();
      console.log(`✅ Service initialized: ${this.isReady()}`);

      // Perform tracking operation (this previously caused validation to fail)
      const trackingData = this.createTrackingData('validation-fix-test-component', 85);
      await this.track(trackingData);
      console.log('✅ Tracking operation completed');

      // Now validate - this should return 'valid' status
      const validationResult = await this.validate();
      console.log(`Validation Status: ${validationResult.status}`);
      console.log(`Overall Score: ${validationResult.overallScore}`);
      console.log(`Errors: ${validationResult.errors.length}`);

      // Cleanup
      await this.shutdown();

      return validationResult.status === 'valid';
    } catch (error) {
      console.error('Test failed:', error);
      return false;
    }
  }
}

async function testValidationFix() {
  console.log('🔧 Testing Service Lifecycle Compatibility fix...\n');

  const service = new TestValidationFixService();
  const success = await service.testValidationFix();

  console.log('\n🎯 VALIDATION FIX TEST RESULT:');
  console.log(`  Status: ${success ? '✅ FIXED - Validation returns valid' : '❌ STILL FAILING'}`);

  if (success) {
    console.log('\n🎉 SUCCESS! Service Lifecycle Compatibility test should now pass.');
    console.log('The BaseTrackingService validation fix is working correctly.');
  } else {
    console.log('\n⚠️  The fix may need additional work.');
  }

  // Force exit to prevent hanging
  setTimeout(() => {
    console.log('\n🔄 Exiting...');
    process.exit(success ? 0 : 1);
  }, 1000);
}

// Run the test
if (require.main === module) {
  testValidationFix().catch(console.error);
}

export { TestValidationFixService, testValidationFix };
