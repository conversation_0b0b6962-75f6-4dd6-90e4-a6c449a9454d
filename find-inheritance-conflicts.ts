/**
 * <PERSON><PERSON><PERSON> to systematically identify inheritance conflicts in BaseTrackingService subclasses
 * Scans for common conflicting properties and methods
 */

import * as fs from 'fs';
import * as path from 'path';

interface ConflictReport {
  filePath: string;
  className: string;
  conflicts: Array<{
    type: 'property' | 'method';
    name: string;
    line: number;
    context: string;
  }>;
}

// Common conflicting properties from MemorySafeResourceManager and BaseTrackingService
const CONFLICTING_PROPERTIES = [
  '_isShuttingDown',
  '_isInitialized', 
  '_config',
  '_metrics',
  '_errors',
  '_warnings',
  '_resources',
  '_limits',
  '_isShutdown', // Common variation
  '_isReady'     // Common variation
];

// Common conflicting methods
const CONFLICTING_METHODS = [
  'initialize',
  'shutdown',
  'cleanup',
  'isHealthy',
  'getResourceMetrics'
];

function scanFileForConflicts(filePath: string): ConflictReport | null {
  try {
    const content = fs.readFileSync(filePath, 'utf-8');
    const lines = content.split('\n');
    
    // Check if file extends BaseTrackingService
    const extendsBaseTracking = content.includes('extends BaseTrackingService');
    if (!extendsBaseTracking) {
      return null;
    }

    // Extract class name
    const classMatch = content.match(/export class (\w+) extends BaseTrackingService/);
    if (!classMatch) {
      return null;
    }

    const className = classMatch[1];
    const conflicts: ConflictReport['conflicts'] = [];

    // Scan for conflicting properties
    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      
      // Check for private property declarations
      CONFLICTING_PROPERTIES.forEach(prop => {
        const privatePattern = new RegExp(`private\\s+${prop}\\s*[:=]`);
        const protectedPattern = new RegExp(`protected\\s+${prop}\\s*[:=]`);
        
        if (privatePattern.test(trimmedLine) || protectedPattern.test(trimmedLine)) {
          conflicts.push({
            type: 'property',
            name: prop,
            line: index + 1,
            context: trimmedLine
          });
        }
      });

      // Check for conflicting method declarations
      CONFLICTING_METHODS.forEach(method => {
        const methodPattern = new RegExp(`(private|protected|public)\\s+.*${method}\\s*\\(`);
        
        if (methodPattern.test(trimmedLine) && !trimmedLine.includes('super.')) {
          conflicts.push({
            type: 'method',
            name: method,
            line: index + 1,
            context: trimmedLine
          });
        }
      });
    });

    if (conflicts.length > 0) {
      return {
        filePath,
        className,
        conflicts
      };
    }

    return null;
  } catch (error) {
    console.error(`Error scanning ${filePath}:`, error);
    return null;
  }
}

function scanDirectory(dirPath: string): ConflictReport[] {
  const reports: ConflictReport[] = [];
  
  function scanRecursive(currentPath: string) {
    const items = fs.readdirSync(currentPath);
    
    for (const item of items) {
      const fullPath = path.join(currentPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scanRecursive(fullPath);
      } else if (stat.isFile() && item.endsWith('.ts') && !item.endsWith('.test.ts')) {
        const report = scanFileForConflicts(fullPath);
        if (report) {
          reports.push(report);
        }
      }
    }
  }
  
  scanRecursive(dirPath);
  return reports;
}

function generateReport(reports: ConflictReport[]): string {
  let output = `
🔍 INHERITANCE CONFLICT ANALYSIS REPORT
========================================

Found ${reports.length} services with potential inheritance conflicts:

`;

  reports.forEach((report, index) => {
    output += `
${index + 1}. ${report.className}
   File: ${report.filePath}
   Conflicts: ${report.conflicts.length}
   
`;

    report.conflicts.forEach(conflict => {
      output += `   ❌ ${conflict.type.toUpperCase()}: ${conflict.name} (line ${conflict.line})
      Context: ${conflict.context}
`;
    });
  });

  output += `

🛠️ RECOMMENDED FIXES:
=====================

For each conflicting property, apply the anti-simplification pattern:

1. **Rename conflicting properties** (don't remove them):
   - _isShuttingDown → _[serviceName]ShuttingDown
   - _isInitialized → _[serviceName]Initialized  
   - _config → _[serviceName]Config
   - _metrics → _[serviceName]Metrics

2. **Update all references** to use the new property names

3. **Preserve functionality** - both inherited and service-specific logic

4. **Test compilation** after each fix

Example fix pattern:
\`\`\`typescript
// ❌ BEFORE (Conflicting):
private _isShuttingDown = false;

// ✅ AFTER (Renamed):
private _analyticsShuttingDown = false;
\`\`\`
`;

  return output;
}

// Main execution
if (require.main === module) {
  const serverPath = './server/src/platform';
  
  console.log('🔍 Scanning for inheritance conflicts in BaseTrackingService subclasses...');
  
  const reports = scanDirectory(serverPath);
  const reportContent = generateReport(reports);
  
  console.log(reportContent);
  
  // Save report to file
  fs.writeFileSync('./inheritance-conflicts-report.md', reportContent);
  console.log('\n📄 Report saved to: inheritance-conflicts-report.md');
}

export { scanFileForConflicts, scanDirectory, generateReport };
